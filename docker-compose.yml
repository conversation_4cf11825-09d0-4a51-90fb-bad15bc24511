
services:
  web:
    build: .
    ports:
      - "8000:8000"
    env_file:
      - .env
    volumes:
      - .:/app
    command: >
      sh -c "
        echo 'Waiting for database to be ready...' &&
        sleep 10 &&
        echo 'Creating database tables...' &&
        python /app/create_tables.py &&
        chmod +x /app/run_migrations.sh &&
        bash /app/run_migrations.sh &&
        uvicorn main:app --host 0.0.0.0 --port 8000 --reload
      "
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - ADMIN_SECRET_KEY=${ADMIN_SECRET_KEY}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - PPCA_CA_ASSISTANT_ID=${PPCA_CA_ASSISTANT_ID}
      - MOA_ASSISTANT_ID=${MOA_ASSISTANT_ID}
      - DE_PPCA_CA_ASSISTANT_ID=${DE_PPCA_CA_ASSISTANT_ID}
      - DE_MOA_ASSISTANT_ID=${DE_MOA_ASSISTANT_ID}
      - AZURE_BASE_OPENAI_ENDPOINT=${AZURE_BASE_OPENAI_ENDPOINT}
      - AZURE_OPENAI_API_VERSION=${AZURE_OPENAI_API_VERSION}
      - MAIL_USERNAME=${MAIL_USERNAME}
      - MAIL_PASSWORD=${MAIL_PASSWORD}
      - MAIL_FROM=${MAIL_FROM}
      - ADMIN_NOTIFICATION_EMAIL=${ADMIN_NOTIFICATION_EMAIL}
    network_mode: "bridge"  # Use the default bridge network where your PostgreSQL container is running

  db-setup:
    build: .
    env_file:
      - .env
    environment:
      - DATABASE_URL=${DATABASE_URL}
    command: python /app/create_tables.py
    network_mode: "bridge"
    profiles:
      - db-setup

volumes:
  postgres_data:
