name: CICD-backend

on:
  push:
    branches: [dev]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout source
        uses: actions/checkout@v3

      - name: Login to Docker Hub
        run: echo "${{ secrets.DOCKER_PASSWORD }}" | docker login -u "${{ secrets.DOCKER_USERNAME }}" --password-stdin

      - name: Build Docker image
        run: docker build -t marcelk15/cicd-backend-dev .

      - name: Publish image to Docker Hub
        run: docker push marcelk15/cicd-backend-dev:latest
        
  deploy:
    needs: build
    runs-on: dev-backend
    steps:
      - name: Login to Docker Hub
        run: echo "${{ secrets.DOCKER_PASSWORD }}" | docker login -u "${{ secrets.DOCKER_USERNAME }}" --password-stdin
  
      - name: Pull image from Docker Hub
        run: docker pull marcelk15/cicd-backend-dev:latest
  
      - name: Copy .env file to workflow directory
        run: |
          cp $HOME/.env.dev ./.env
          echo "Copied .env.dev file to current directory"
          ls -la
  
      - name: List containers before deletion
        run: docker ps -a
  
      - name: Delete old container
        run: docker rm -f cicd-backend-dev || true
  
      - name: List containers after deletion
        run: docker ps -a
  
      - name: Run Docker container
        run: |
          docker run -d -p 8000:8000 \
            --name cicd-backend-dev \
            --env-file ./.env \
            marcelk15/cicd-backend-dev:latest

      - name: Remove dangling Docker images
        run: docker image prune -f
