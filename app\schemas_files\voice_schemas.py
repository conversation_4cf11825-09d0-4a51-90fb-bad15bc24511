"""Voice service schemas"""
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime

class TranscriptMessage(BaseModel):
    """Schema for transcript messages"""
    role: str
    content: str
    timestamp: str
    source: Optional[str] = None
    is_placeholder: Optional[bool] = False

class ConversationSession(BaseModel):
    """Schema for conversation sessions"""
    messages: List[TranscriptMessage]
    started_at: str
    ended_at: str
    metadata: Dict[str, Any]
    tokens_used: int = 0

class VoiceSessionConfig(BaseModel):
    """Schema for voice session configuration"""
    modalities: List[str] = ["audio", "text"]
    instructions: str
    voice: str
    input_audio_format: str = "pcm16"
    output_audio_format: str = "pcm16"
    input_audio_transcription: Dict[str, str] = {"model": "whisper-1"}
    turn_detection: Dict[str, Any]

class WebSocketMessage(BaseModel):
    """Schema for WebSocket messages"""
    type: str
    data: Optional[Any] = None
    audio: Optional[str] = None

class AgentContext(BaseModel):
    """Schema for agent context"""
    user_id: int
    agent_role: str
    language: str = "en"
    user_age: Optional[int] = None
    user_first_name: Optional[str] = None
    user_last_name: Optional[str] = None
    user_birthdate: Optional[str] = None
    user_org_name: Optional[str] = None
    user_phone_number: Optional[str] = None
    personalized_instructions: Optional[str] = None
