from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status, Security
from sqlalchemy.orm import Session
from fastapi.security import OAuth2P<PERSON>wordRequestForm, OAuth2PasswordBearer
from .. import schemas, crud, auth, models
from ..database import get_db
from ..auth import get_current_user
from ..services.email_service import email_service
import os
from datetime import <PERSON><PERSON><PERSON>
from typing import Optional
from pydantic import BaseModel
from jose import JWTError
from ..logger import app_logger as logger


    
# Add these new schema classes at the top
class ChangePasswordRequest(BaseModel):
    current_password: str
    new_password: str

class ForgotPasswordRequest(BaseModel):
    email: str

class ResetPasswordRequest(BaseModel):
    token: str
    new_password: str

router = APIRouter(prefix="/auth", tags=["auth"])

@router.post('/signup', response_model=schemas.UserOut)
async def signup(user_in: schemas.UserCreate, db: Session = Depends(get_db)):
    logger.info(f"New user signup attempt: {user_in.email}")
    existing = crud.get_user_by_email(db, email=user_in.email)
    if existing:
        logger.warning(f"Signup failed: Email already registered: {user_in.email}")
        raise HTTPException(status_code=400, detail="Email already registered")
    user = crud.create_user(db, user_in)
    logger.info(f"User created successfully: {user.email}")

    # Send admin notification email
    try:
        await email_service.send_admin_notification_email(user.email, user.details.first_name)
    except Exception as e:
        logger.error(f"Failed to send admin notification for user {user.email}: {str(e)}")
        # Don't fail the signup if email fails

    return user

@router.post('/login', response_model=schemas.Token)
def login(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    user = crud.get_user_by_email(db, form_data.username)
    if not user or not auth.verify_password(form_data.password, user.hashed_password):
        raise HTTPException(status_code=401, detail="Invalid credentials")
    access_token = auth.create_access_token(data={"sub": user.email})
    return {"access_token": access_token, "token_type": "bearer"}

@router.post('/signup/admin', response_model=schemas.UserOut)
def signup_admin(
    user_in: schemas.UserCreate, 
    admin_key: str,
    db: Session = Depends(get_db)
):
    # Get ADMIN_SECRET_KEY from configuration table with fallback to environment variable
    admin_secret_config = crud.get_configuration_by_key(db, "ADMIN_SECRET_KEY")
    admin_secret = admin_secret_config.value if admin_secret_config else os.getenv("ADMIN_SECRET_KEY")
    
    if not admin_secret or admin_key != admin_secret:
        raise HTTPException(
            status_code=403,
            detail="Invalid admin creation key"
        )

    existing = crud.get_user_by_email(db, email=user_in.email)
    if existing:
        raise HTTPException(
            status_code=400, 
            detail="Email already registered"
        )
    
    # Create admin user
    user = crud.create_admin_user(db, user_in)
    return user

# Add these new endpoints
@router.post('/change-password')
def change_password(
    request: ChangePasswordRequest,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify current password
    if not auth.verify_password(request.current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect"
        )
    
    # Update password
    current_user.hashed_password = auth.get_password_hash(request.new_password)
    db.commit()
    return {"message": "Password updated successfully"}

@router.post('/forgot-password')
async def forgot_password(request: ForgotPasswordRequest, db: Session = Depends(get_db)):
    user = crud.get_user_by_email(db, request.email)
    if not user:
        # Return success even if email doesn't exist (security best practice)
        return {"message": "If your email is registered, you will receive a password reset link"}
    
    # Generate password reset token
    reset_token = auth.create_access_token(
        data={"sub": user.email, "type": "password_reset"},
        expires_delta=timedelta(minutes=15)
    )

    # Send password reset email using email service
    email_sent = await email_service.send_password_reset_email(
        user.email,
        user.details.first_name,
        reset_token
    )

    if email_sent:
        logger.info(f"Password reset email sent to: {user.email}")
    else:
        logger.error(f"Failed to send password reset email to: {user.email}")

    return {"message": "Password reset instructions sent"}

@router.post('/reset-password')
def reset_password(request: ResetPasswordRequest, db: Session = Depends(get_db)):
    try:
        # Verify token and get email
        payload = auth.verify_token(request.token)
        if payload.get("type") != "password_reset":
            raise HTTPException(status_code=400, detail="Invalid token type")
        
        email = payload.get("sub")
        if not email:
            raise HTTPException(status_code=400, detail="Invalid token payload")
            
        user = crud.get_user_by_email(db, email)
        if not user:
            raise HTTPException(status_code=400, detail="User not found")
        
        # Update password
        user.hashed_password = auth.get_password_hash(request.new_password)
        db.commit()
        logger.info(f"Password reset successfully for user: {email}")
        return {"message": "Password reset successfully"}
        
    except JWTError as e:
        logger.warning(f"Password reset failed - Invalid token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired token"
        )
    except Exception as e:
        logger.error(f"Password reset failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while resetting password"
        )

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/login")

@router.post("/logout")
async def logout(
    token: str = Security(oauth2_scheme),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Logout the current user by blacklisting their token
    """
    auth.blacklist_token(db, token)
    return {"message": "Successfully logged out"}
