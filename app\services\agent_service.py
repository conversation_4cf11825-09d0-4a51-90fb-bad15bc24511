"""Agent instruction service"""
import json
from typing import List, Dict
from sqlalchemy.orm import Session
from sqlalchemy import desc
from datetime import datetime

from app import crud, models
from app.azure_assistants import run_assistant
from app.config.voice_config import VoiceConfig
from app.schemas_files.voice_schemas import AgentContext
from app.exceptions.voice_exceptions import AgentInstructionException
from app.logger import app_logger as logger

class AgentInstructionService:
    """Service for managing agent instructions and context"""

    def __init__(self):
        self.config = VoiceConfig()

    async def get_agent_instructions(
        self,
        db: Session,
        context: AgentContext,
        for_audio_text: bool = False
    ) -> str:
        """Get agent instructions based on context"""
        try:
            agent = crud.get_agent_by_role(db, context.agent_role, context.language)

            if context.agent_role == "oba":
                return self._get_oba_instructions(agent, context)
            elif context.agent_role == "fa":
                return self._get_fa_instructions(agent, context)
            elif context.agent_role == "cm_fa":
                return self._get_cm_fa_instructions(agent, context)
            elif context.agent_role == "ppca":
                return await self._get_ppca_instructions(db, agent, context, for_audio_text)
            elif agent:
                return agent.instructions
            else:
                logger.warning(f"Agent role '{context.agent_role}' not found")
                return ""

        except Exception as e:
            logger.error(f"Error getting agent instructions: {e}", exc_info=True)
            raise AgentInstructionException(f"Failed to get agent instructions: {e}")

    def _get_oba_instructions(self, agent, context: AgentContext) -> str:
        """Get OBA (Onboarding Agent) instructions"""
        if not agent:
            return ""

        # Create user context based on language
        if context.language == "de":
            age_text = f" ist {context.user_age} Jahre alt" if context.user_age is not None else ""
            user_context = (
                f"Kontext zum Nutzer: {context.user_first_name}{age_text} "
                f"hat Demenz im frühen bis mittleren Stadium. Grüße {context.user_first_name} "
                f"freundlich und erkläre was du nun mit dem Nutzer vor hast. "
                f"Dann beginne mit dem Kennlerngespräch."
            )
        else:
            age_text = f" is {context.user_age} years old" if context.user_age is not None else ""
            user_context = (
                f"Context about the user: {context.user_first_name}{age_text} "
                f"and potentially has early stage dementia. Greet {context.user_first_name} "
                f"kindly, introduce yourself and what you are going to do with {context.user_first_name} "
                f"now. Then begin with the onboarding conversation."
            )

        return f"<text instruction start>{user_context}\n\n{agent.instructions}<text instruction end>"

    def _get_fa_instructions(self, agent, context: AgentContext) -> str:
        """Get FA (Functional Assessment) instructions"""
        if not agent:
            return ""

        # Create user context based on language
        if context.language == "de":
            age_text = f" ist {context.user_age} Jahre alt" if context.user_age is not None else ""
            user_context = (
                f"Kontext zum Nutzer: {context.user_first_name}{age_text} "
                f"hat Demenz im frühen bis mittleren Stadium. Grüße {context.user_first_name} "
                f"freundlich und erkläre was du nun mit dem Nutzer vor hast. "
                f"Dann beginne mit dem Kennlerngespräch."
            )
        else:
            age_text = f" is {context.user_age} years old" if context.user_age is not None else ""
            user_context = (
                f"Context about the user: {context.user_first_name}{age_text} "
                f"and potentially has early stage dementia. Greet {context.user_first_name} "
                f"kindly, introduce yourself and what you are going to do with {context.user_first_name} "
                f"now. Then begin with the onboarding conversation."
            )

        return user_context + agent.instructions

    def _get_cm_fa_instructions(self, agent, context: AgentContext) -> str:
        """Get CM_FA (Common Feedback Assistant) instructions"""
        if not agent:
            return ""

        # Create user context based on language
        if context.language == "de":
            age_text = f" ist {context.user_age} Jahre alt" if context.user_age is not None else ""
            user_context = (
                f"Kontext zum Nutzer: {context.user_first_name}{age_text} "
                f"hat Demenz im frühen bis mittleren Stadium. Grüße {context.user_first_name} "
                f"freundlich und erkläre was du nun mit dem Nutzer vor hast. "
                f"Dann beginne mit dem Kennlerngespräch."
            )
        else:
            age_text = f" is {context.user_age} years old" if context.user_age is not None else ""
            user_context = (
                f"Context about the user: {context.user_first_name}{age_text} "
                f"and potentially has early stage dementia. Greet {context.user_first_name} "
                f"kindly, introduce yourself and what you are going to do with {context.user_first_name} "
                f"now. Then begin with the onboarding conversation."
            )

        return user_context + agent.instructions

    async def _get_ppca_instructions(
        self,
        db: Session,
        agent,
        context: AgentContext,
        for_audio_text: bool = False
    ) -> str:
        """Get PPCA (Personalized Care Assistant) instructions"""
        # First try to get personalized instructions
        personalized_assistant = db.query(models.Assistant).filter(
            models.Assistant.user_id == context.user_id,
            models.Assistant.agent_role == "ppca"
        ).first()

        # If no personalized assistant found, wait and recheck (for race condition)
        if not personalized_assistant:
            logger.info("No personalized_assistant found, waiting and rechecking...")
            import asyncio
            await asyncio.sleep(1)  # Wait 1 second

            # Recheck after waiting
            personalized_assistant = db.query(models.Assistant).filter(
                models.Assistant.user_id == context.user_id,
                models.Assistant.agent_role == "ppca"
            ).first()

            if personalized_assistant:
                logger.info("personalized_assistant found after recheck")
            else:
                logger.info("personalized_assistant still not found after recheck")

        # For audio/text requests, return only clean personalized instructions
        if for_audio_text:
            if personalized_assistant and personalized_assistant.instructions:
                return self._clean_instruction_tags(str(personalized_assistant.instructions))
            elif agent and agent.instructions:
                return self._clean_instruction_tags(str(agent.instructions))
            else:
                return self._get_default_instructions(context.language)

        # Log personalized assistant info safely
        if personalized_assistant:
            logger.info(f"personalized_assistant found with instructions: {personalized_assistant.instructions}")
        else:
            logger.info("No personalized_assistant found for user")

        # For MOA processing, prepare instructions with tags and transcripts
        base_instructions = ""
        if personalized_assistant and personalized_assistant.instructions:
            # Use personalized instructions with tags for MOA
            base_instructions = f"{str(personalized_assistant.instructions)}"
            logger.info(f"Using personalized instructions for user: {context.user_id}")
        else:
            # Fall back to default instructions with tags for MOA
            if not agent or not agent.instructions:
                default_instructions = self._get_default_instructions(context.language)
                base_instructions = f"{default_instructions}"
            else:
                base_instructions = f"{str(agent.instructions)}"
                agent_name = agent.name if agent else "unknown"
                logger.info(f"Using default instructions for agent: {agent_name}")

        # Add conversation history context (3 transcripts)
        conversation_context = await self._get_conversation_context(db, context.user_id)
        full_instructions = base_instructions + "\n" +conversation_context

        logger.info(f"full_instructions: {full_instructions}")
        # Generate MOA instructions
        try:
            moa_instructions = await self._generate_moa_instructions(
                full_instructions, context.language, context.user_first_name
            )

            logger.info(f"moa_instructions: {moa_instructions}")
            return moa_instructions
        except Exception as e:
            logger.error(f"Error generating MOA instructions: {e}", exc_info=True)
            return full_instructions

    def _clean_instruction_tags(self, instructions: str) -> str:
        """Clean instruction tags from text"""
        tags_to_remove = [
            # English tags
            "</Text Instruction End>", "<Text Instruction Start>",
            "</text instruction end>", "<text instruction start>",
            # German tags - corrected according to feedback
            "</Textinstruktion Ende>", "<Textinstruktion Start>",
            "</textinstruktion ende>", "<textinstruktion start>",
            # Legacy German tags (keeping for backward compatibility)
            "</Text Anweisung Ende>", "<Text Anweisung Start>",
            "</text anweisung ende>", "<text anweisung start>"
        ]

        cleaned = instructions
        for tag in tags_to_remove:
            cleaned = cleaned.replace(tag, "")

        return cleaned.strip()

    def _clean_moa_context_tags(self, moa_output: str, language: str) -> str:
        """Clean MOA context tags and replace with appropriate labels"""
        # Define context tag patterns to replace
        context_tags_to_replace = [
            ("<Kontext Start>", "Kontext zum Nutzer:" if language == "de" else "Context about the user:"),
            ("<kontext start>", "Kontext zum Nutzer:" if language == "de" else "Context about the user:"),
            ("<Context Start>", "Context about the user:" if language == "en" else "Kontext zum Nutzer:"),
            ("<context start>", "Context about the user:" if language == "en" else "Kontext zum Nutzer:"),
        ]

        # Define end tags to remove - corrected according to feedback
        end_tags_to_remove = [
            # German tags
            "</Kontext Ende>", "</kontext ende>",
            "<Kontext Ende>", "<kontext ende>",
            # English tags
            "</Context End>", "</context end>",
            "<Context End>", "<context end>",
            # Legacy tags (keeping for backward compatibility)
            "</Kontext End>", "</kontext end>",
        ]

        cleaned = moa_output

        # Replace start tags with appropriate labels
        for old_tag, new_label in context_tags_to_replace:
            cleaned = cleaned.replace(old_tag, new_label)

        # Remove end tags
        for tag in end_tags_to_remove:
            cleaned = cleaned.replace(tag, "")

        return cleaned.strip()

    def _get_fallback_moa_context(self, language: str, user_first_name: str = "") -> str:
        """Get fallback MOA context when API timeout occurs"""
        first_name = user_first_name if user_first_name else "User"

        if language == "de":
            fallback_context = (
                f"Kontext zum Nutzer: {first_name} hat noch keinen Anhaltspunkt für ein sinnvolles Gesprächsthema geliefert. "
                f"Leite das Gespräch mit folgender Frage ein: Was ist eine schöne Erinnerung aus deiner Kindheit, die dir immer wieder in den Sinn kommt? "
                f"Bevor du das Gesprächsthema einleitest, grüße {first_name} freundlich und erkundige dich, wie es ihm geht. "
                f"Warte auf die Antwort und reagiere empathisch darauf. Leite das Gesprächsthema erst ein, wenn du weisst, dass es {first_name} gut geht."
            )
        else:
            fallback_context = (
                f"Context about the user: {first_name} has not yet provided any indication for a meaningful conversation topic. "
                f"Start the conversation with the following question: What is a happy memory from your childhood that often comes to mind? "
                f"Before introducing the conversation topic, greet {first_name} warmly and ask how they are. "
                f"Wait for the response and react with empathy. Only introduce the conversation topic once you know that {first_name} is feeling well."
            )

        return fallback_context

    def _get_default_instructions(self, language: str) -> str:
        """Get default instructions based on language"""
        if language == "de":
            return "Du bist ein hilfsbereiter Assistent. Ich bin hier, um dir bei deinen täglichen Aufgaben zu helfen."
        else:
            return "You are a helpful assistant. I am here to help you with your daily tasks."

    async def _get_conversation_context(self, db: Session, user_id: int) -> str:
        """Get recent conversation context for PPCA"""
        try:
            # Get PPCA conversations (latest first)
            ppca_conversations = db.query(models.Conversation).filter(
                models.Conversation.user_id == user_id,
                models.Conversation.agent_role == "ppca"
            ).order_by(desc(models.Conversation.updated_at)).limit(3).all()

            # Get onboarding conversation
            onboarding_conversation = db.query(models.Conversation).filter(
                models.Conversation.user_id == user_id,
                models.Conversation.agent_role == "oba"
            ).order_by(desc(models.Conversation.updated_at)).first()

            if not ppca_conversations and not onboarding_conversation:
                logger.info("No previous conversations found")
                return ""

            # Collect all sessions first
            all_sessions = []
            max_transcripts = 3

            # Process PPCA conversations first (latest first)
            for conversation in ppca_conversations:
                if not conversation.conversation_data:
                    continue

                conversation_data = conversation.conversation_data
                if isinstance(conversation_data, str):
                    try:
                        conversation_data = json.loads(conversation_data)
                    except json.JSONDecodeError:
                        logger.warning(f"Failed to parse conversation data for conversation {conversation.id}")
                        continue

                sessions = conversation_data.get("sessions", [])
                for session in sessions:
                    if "messages" not in session or not session["messages"]:
                        continue

                    formatted_messages = self._format_session_messages(session["messages"])
                    if formatted_messages:
                        all_sessions.append({
                            "formatted_messages": formatted_messages,
                            "conversation_updated_at": conversation.updated_at
                        })

            # Sort PPCA sessions by timestamp (latest first)
            all_sessions.sort(key=lambda x: x["conversation_updated_at"], reverse=True)

            # Take up to 3 PPCA sessions
            final_sessions = all_sessions[-3:]

            # If we have less than 3 PPCA sessions, add OBA as the last transcript
            if len(final_sessions) < max_transcripts and onboarding_conversation and onboarding_conversation.conversation_data:
                conversation_data = onboarding_conversation.conversation_data

                if isinstance(conversation_data, str):
                    try:
                        conversation_data = json.loads(conversation_data)
                    except json.JSONDecodeError:
                        logger.info(f"Failed to parse onboarding conversation data")
                        conversation_data = None

                if isinstance(conversation_data, dict):
                    sessions = conversation_data.get("sessions", [])
                    for session in sessions:
                        if "messages" not in session or not session["messages"]:
                            continue

                        formatted_messages = self._format_session_messages(session["messages"])
                        if formatted_messages:
                            final_sessions.insert(0, {
                                "formatted_messages": formatted_messages,
                                "conversation_updated_at": onboarding_conversation.updated_at
                            })
                            break  # Only add one OBA session

            if not final_sessions:
                logger.info("No valid sessions found in conversations")
                return ""

            # Build context parts with proper transcript numbering (1 = latest, 3 = oldest)
            context_parts = []
            final_sessions = final_sessions[::-1]
            for i, session_data in enumerate(final_sessions):
                transcript_num = i + 1
                transcript_json = {"messages": session_data["formatted_messages"]}
                transcript_str = (
                    f"<Transcript {transcript_num} Start>\n\n"
                    f"{json.dumps(transcript_json, indent=2)}\n"
                    f"</Transcript {transcript_num} End>\n"
                )
                context_parts.append(transcript_str)

            return "\n\n".join(context_parts)

        except Exception as e:
            logger.error(f"Error getting conversation context: {e}", exc_info=True)
            return ""

    def _format_session_messages(self, messages: List[Dict]) -> List[Dict]:
        """Format session messages for context"""
        formatted_messages = []

        for msg in messages:
            if "role" not in msg or "content" not in msg:
                continue

            content = msg["content"].strip()
            if not content:
                continue

            role = msg["role"].lower()
            if role not in ["assistant", "user"]:
                continue

            formatted_messages.append({
                "role": role,
                "message": content
            })

        return formatted_messages

    async def _generate_moa_instructions(self, instructions: str, language: str, user_first_name: str = "") -> str:
        """Generate MOA (Meta-Orchestration Agent) instructions"""
        logger.info(f"Generating MOA instructions for language: {language}")
        try:
            assistant_id = (
                self.config.DE_MOA_ASSISTANT_ID if language == "de"
                else self.config.MOA_ASSISTANT_ID
            )

            if not assistant_id:
                logger.info(f"MOA assistant ID not configured for language: {language}")
                return self._get_fallback_moa_context(language, user_first_name)

            logger.info(f"instructions------: {instructions}")
            moa_instructions = await run_assistant("moa", assistant_id, instructions)

            # Clean MOA context tags and replace with appropriate labels
            cleaned_moa_instructions = self._clean_moa_context_tags(moa_instructions, language)

            return cleaned_moa_instructions

        except TimeoutError as e:
            logger.warning(f"MOA API timeout after 20 seconds: {e}")
            return self._get_fallback_moa_context(language, user_first_name)
        except Exception as e:
            logger.error(f"Error generating MOA instructions: {e}", exc_info=True)
            return self._get_fallback_moa_context(language, user_first_name)

    async def store_ppca_instructions(
        self,
        db: Session,
        user_id: int,
        agent_role: str,
        language: str,
        conversation_data: str
    ):
        """Store PPCA instructions for future use"""
        try:
            if not conversation_data:
                logger.info("No transcript to store")
                return

            assistant_id = (
                self.config.DE_PPCA_CA_ASSISTANT_ID if language == "de"
                else self.config.PPCA_CA_ASSISTANT_ID
            )

            if not assistant_id:
                logger.warning(f"PPCA assistant ID not configured for language: {language}")
                return

            instructions = await run_assistant(agent_role, assistant_id, conversation_data)

            if not instructions or not user_id or not agent_role:
                return

            # Check if assistant already exists
            existing_assistant = db.query(models.Assistant).filter(
                models.Assistant.user_id == user_id,
                models.Assistant.agent_role == agent_role
            ).first()

            if existing_assistant:
                # Update existing assistant
                existing_assistant.instructions = instructions
                existing_assistant.updated_at = datetime.utcnow()
                logger.info(f"Updated assistant instructions for user_id: {user_id}, agent_role: {agent_role}")
            else:
                # Create new assistant
                new_assistant = models.Assistant(
                    user_id=user_id,
                    agent_role=agent_role,
                    instructions=instructions
                )
                db.add(new_assistant)
                logger.info(f"Created new assistant for user_id: {user_id}, agent_role: {agent_role}")

            db.commit()

        except Exception as e:
            logger.error(f"Error storing instructions: {e}", exc_info=True)
            db.rollback()

    def get_initial_message(self, agent_role: str, language: str, moa_context: str = "", user_context: str = "") -> str:
        """Get initial message based on agent role and language"""
        if agent_role in ["oba", "fa", "cm_fa"]:
            # For OBA, FA, and CM_FA, use user context instead of simple greeting
            if user_context:
                return user_context
            else:
                return "Hi" if language == "en" else "Hallo"
        elif agent_role == "ppca":
            # For PPCA, return the moa_context as is (this contains the MOA processed instructions)
            return moa_context
        else:
            return ""
