#!/bin/bash

echo "Using DATABASE_URL: ${DATABASE_URL}"
echo "Running database migrations..."
export PYTHONPATH=$PYTHONPATH:/app
cd /app

# Check if alembic directory exists
if [ ! -d "alembic" ]; then
    echo "Alembic directory not found. Initializing Alembic..."
    alembic init alembic
    
    # Update the env.py file to use our models and database URL
    sed -i 's/target_metadata = None/from app.database import Base\ntarget_metadata = Base.metadata/' alembic/env.py
    sed -i '/import os/d' alembic/env.py
    sed -i '/from dotenv import load_dotenv/d' alembic/env.py
    sed -i '/fileConfig/a import os\nfrom dotenv import load_dotenv\n\n# Load environment variables\nload_dotenv()\n\n# Set the database URL from environment variable\ndatabase_url = os.getenv("DATABASE_URL")\nif database_url:\n    config.set_main_option("sqlalchemy.url", database_url)' alembic/env.py
    
    echo "Alembic initialized."
fi

# Check if script.py.mako exists
if [ ! -f "alembic/script.py.mako" ]; then
    echo "script.py.mako not found. Reinitializing Alembic..."
    rm -rf alembic
    alembic init alembic
    
    # Update the env.py file to use our models and database URL
    sed -i 's/target_metadata = None/from app.database import Base\ntarget_metadata = Base.metadata/' alembic/env.py
    sed -i '/import os/d' alembic/env.py
    sed -i '/from dotenv import load_dotenv/d' alembic/env.py
    sed -i '/fileConfig/a import os\nfrom dotenv import load_dotenv\n\n# Load environment variables\nload_dotenv()\n\n# Set the database URL from environment variable\ndatabase_url = os.getenv("DATABASE_URL")\nif database_url:\n    config.set_main_option("sqlalchemy.url", database_url)' alembic/env.py
    
    echo "Alembic reinitialized."
fi

# Create versions directory if it doesn't exist
mkdir -p alembic/versions

# Check if there are any migration versions
if [ -z "$(ls -A alembic/versions 2>/dev/null)" ]; then
    echo "No migration versions found. Creating initial migration..."
    alembic revision --autogenerate -m "Initial migration"
    echo "Initial migration created."
fi

# Run migrations
echo "Applying migrations..."
alembic upgrade head
echo "Migrations completed!"
