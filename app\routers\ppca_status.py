from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app import models, crud
from app.auth import get_current_user
from app.database import get_db
from app.logger import app_logger as logger

router = APIRouter(prefix="/assistants", tags=["ppca"])

@router.get("/status")
async def get_ppca_instructions_status(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get PPCA instructions generation status for current user
    Returns:
    - status: 'not_started', 'in_progress', 'complete'
    - message: Status description
    - has_instructions: Whether user has PPCA instructions available
    """
    try:
        # Check if user has PPCA instructions
        assistant = db.query(models.Assistant).filter(
            models.Assistant.user_id == current_user.id,
            models.Assistant.agent_role == "ppca"
        ).first()

        # Check if user has completed OBA conversation
        oba_conversation = db.query(models.Conversation).filter(
            models.Conversation.user_id == current_user.id,
            models.Conversation.agent_role == "oba"
        ).first()

        if not oba_conversation:
            return {
                "status": "not_started",
                "message": "Complete onboarding conversation first",
                "has_instructions": False,
                "can_start_ppca": False
            }

        if assistant and assistant.instructions:
            return {
                "status": "complete",
                "message": "PPCA instructions ready",
                "has_instructions": True,
                "can_start_ppca": True
            }

        # If OBA conversation exists but no assistant instructions,
        # assume it's still in progress (since the async task takes time)
        return {
            "status": "in_progress",
            "message": "Generating personalized instructions...",
            "has_instructions": False,
            "can_start_ppca": False
        }

    except Exception as e:
        logger.error(f"Error checking PPCA instructions status: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to check status")

@router.post("/trigger-instructions-generation")
async def trigger_ppca_instructions_generation(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Manually trigger PPCA instructions generation if it wasn't started
    """
    try:
        # Check if user has OBA conversation
        oba_conversation = db.query(models.Conversation).filter(
            models.Conversation.user_id == current_user.id,
            models.Conversation.agent_role == "oba"
        ).first()

        if not oba_conversation:
            raise HTTPException(
                status_code=400,
                detail="Complete onboarding conversation first"
            )

        # Check if instructions already exist
        assistant = db.query(models.Assistant).filter(
            models.Assistant.user_id == current_user.id,
            models.Assistant.agent_role == "ppca"
        ).first()

        if assistant and assistant.instructions:
            return {
                "status": "already_complete",
                "message": "PPCA instructions already exist"
            }

        # TODO: Trigger the async task here
        # This would need to be implemented with the voice service

        return {
            "status": "triggered",
            "message": "Instructions generation triggered"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error triggering PPCA instructions generation: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to trigger generation")
