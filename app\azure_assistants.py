import os
import time
from openai import AzureOpenAI
from dotenv import load_dotenv
from .logger import app_logger as logger
import asyncio

load_dotenv()


AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY")
AZURE_OPENAI_API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION")
AZURE_BASE_OPENAI_ENDPOINT= os.getenv("AZURE_BASE_OPENAI_ENDPOINT")
PPCA_CA_ASSISTANT_ID = os.getenv("PPCA_CA_ASSISTANT_ID")
MOA_ASSISTANT_ID = os.getenv("MOA_ASSISTANT_ID")


client = AzureOpenAI(
    api_key=AZURE_OPENAI_API_KEY,
    azure_endpoint=AZURE_BASE_OPENAI_ENDPOINT,
    api_version=AZURE_OPENAI_API_VERSION,
)

async def run_assistant(assistant_role, assistant_id, transcript):
    # 1. Create a thread
    thread = client.beta.threads.create()
    logger.info(f"Thread created: {thread.id}")
    
    # 2. Add a message to the thread
    message = client.beta.threads.messages.create(
        thread_id=thread.id,
        role="user",
        content=transcript
    )
    logger.info(f"Message added to thread: {message.id}")

    # 3. Run the assistant on the thread
    run = client.beta.threads.runs.create(
        thread_id=thread.id,
        assistant_id=assistant_id
    )
    logger.info(f"Run started: {run.id}")

    # 4. Poll the run status with timeout and async sleep
    start_time = time.time()
    timeout = 60
    if assistant_role=="moa":
        timeout = 20  # seconds - reduced to 20 seconds as per feedback
    check_count = 0

    while True:
        run_status = client.beta.threads.runs.retrieve(
            thread_id=thread.id,
            run_id=run.id
        )
        if check_count % 4 == 0:
            logger.info(f"Run status: {run_status.status}")

        if run_status.status == "completed":
            break
        elif run_status.status in ["failed", "cancelled", "expired"]:
            raise Exception(f"Run did not complete successfully: {run_status.status}")
        
        if time.time() - start_time > timeout:
            raise TimeoutError("Run exceeded maximum allowed time.")
        
        check_count += 1
        await asyncio.sleep(0.5)

    # 5. Retrieve and return assistant response
    messages = client.beta.threads.messages.list(thread_id=thread.id)
    for msg in reversed(messages.data):  # Most recent first
        if msg.role == "assistant":
            response = msg.content[0].text.value
            logger.info("\nAssistant response:")
            logger.info(response)
            return response
                