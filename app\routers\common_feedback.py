from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional, List
from datetime import datetime

from app.database import get_db
from app.auth import get_current_user
from app.routers.admin import get_current_admin
from app import models, schemas, crud

router = APIRouter(prefix="/common-feedback", tags=["common-feedback"])


@router.get("/", response_model=List[schemas.CommonFeedbackResponse])
def get_common_feedbacks(
    limit: int = Query(30, ge=1, le=100),
    user_id: Optional[int] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_db),
    admin = Depends(get_current_admin)
):
    """
    Get common feedback entries (admin only)
    """
    return crud.get_all_common_feedbacks(
        db=db,
        limit=limit,
        user_id=user_id,
        start_date=start_date,
        end_date=end_date
    )


@router.get("/my-feedback", response_model=List[schemas.CommonFeedbackResponse])
def get_my_common_feedbacks(
    limit: int = Query(30, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get current user's common feedback entries
    """
    return crud.get_common_feedbacks_by_user(
        db=db,
        user_id=current_user.id,
        limit=limit
    )


@router.get("/{feedback_id}", response_model=schemas.CommonFeedbackResponse)
def get_common_feedback(
    feedback_id: int,
    db: Session = Depends(get_db),
    admin = Depends(get_current_admin)
):
    """
    Get a specific common feedback entry by ID (admin only)
    """
    feedback = crud.get_common_feedback_by_id(db, feedback_id)
    if not feedback:
        raise HTTPException(status_code=404, detail="Common feedback not found")
    return feedback


@router.post("/", response_model=schemas.CommonFeedbackResponse)
def create_common_feedback(
    feedback: schemas.CommonFeedbackCreate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Create a new common feedback entry (for testing purposes)
    Note: In normal flow, feedback is created automatically via WebSocket cm_fa conversations
    """
    return crud.create_common_feedback(
        db=db,
        user_id=current_user.id,
        conversation_data=feedback.conversation_data
    )
