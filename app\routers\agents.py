from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app import schemas, models
from app.auth import get_db, get_current_user
from typing import Optional
from pydantic import BaseModel
from datetime import datetime

router = APIRouter(prefix="/agents", tags=["agents"])  # Add prefix to avoid conflicts

# Add admin check dependency
async def get_current_admin(current_user: models.User = Depends(get_current_user)):
    if current_user.role != models.RoleEnum.admin:
        raise HTTPException(status_code=403, detail="Admin access required")
    return current_user

# Create a schema for partial updates
class AgentUpdate(BaseModel):
    name: Optional[str] = None
    instructions: Optional[str] = None
    agent_role: Optional[str] = None
    language: Optional[str] = None  # Add language field

@router.post("/", response_model=schemas.AgentResponse)
def create_agent(agent: schemas.AgentCreate, db: Session = Depends(get_db), admin=Depends(get_current_admin)):
    # Check if agent with same name/role AND same language already exists
    db_agent = db.query(models.Agent).filter(
        ((models.Agent.name == agent.name) & (models.Agent.language == agent.language)) | 
        ((models.Agent.agent_role == agent.agent_role) & (models.Agent.language == agent.language))
    ).first()
    
    if db_agent:
        raise HTTPException(400, "Agent with this name or role already exists in the same language")
    
    new = models.Agent(**agent.dict())
    db.add(new)
    db.commit()
    db.refresh(new)
    return new

@router.get("/", response_model=list[schemas.AgentResponse])
def list_agents(db: Session = Depends(get_db), admin=Depends(get_current_admin)):
    # Now only accessible to admin users
    return db.query(models.Agent).all()

@router.get("/{agent_id}", response_model=schemas.AgentResponse)
def get_agent(agent_id: int, db: Session = Depends(get_db), admin=Depends(get_current_admin)):
    # Now only accessible to admin users
    agent = db.query(models.Agent).filter(models.Agent.id == agent_id).first()
    if not agent:
        raise HTTPException(status_code=404, detail="Agent not found")
    return agent

@router.put("/{agent_id}", response_model=schemas.AgentResponse)
def update_agent(agent_id: str, agent: schemas.AgentCreate, db: Session = Depends(get_db), admin=Depends(get_current_admin)):
    db_agent = db.query(models.Agent).get(agent_id)
    if not db_agent: raise HTTPException(404, "Agent not found")
    
    # Check if agent with same name or role already exists (excluding current agent)
    existing = db.query(models.Agent).filter(
        (models.Agent.id != agent_id) &
        ((models.Agent.name == agent.name) | (models.Agent.agent_role == agent.agent_role))
    ).first()
    
    if existing:
        raise HTTPException(400, "Agent with this name or role already exists")
    
    for k,v in agent.dict().items(): 
        setattr(db_agent, k, v)
    
    # Update the updated_at timestamp
    db_agent.updated_at = datetime.utcnow()
    
    db.commit()
    db.refresh(db_agent)
    return db_agent

@router.delete("/{agent_id}")
def delete_agent(agent_id: str, db: Session = Depends(get_db), admin=Depends(get_current_admin)):
    db_agent = db.query(models.Agent).get(agent_id)
    if not db_agent: raise HTTPException(404, "Agent not found")
    db.delete(db_agent); db.commit()
    return {"detail":"Agent deleted"}

@router.get("/by-role/{agent_role}", response_model=schemas.AgentResponse)
def get_agent_by_role(agent_role: str, db: Session = Depends(get_db), admin=Depends(get_current_admin)):
    agent = db.query(models.Agent).filter(models.Agent.agent_role == agent_role).first()
    if not agent:
        raise HTTPException(status_code=404, detail="Agent not found")
    return agent

@router.get("/by-name/{agent_name}", response_model=schemas.AgentResponse)
def get_agent_by_name(agent_name: str, db: Session = Depends(get_db), admin=Depends(get_current_admin)):
    agent = db.query(models.Agent).filter(models.Agent.name == agent_name).first()
    if not agent:
        raise HTTPException(status_code=404, detail="Agent not found")
    return agent

@router.patch("/{agent_id}", response_model=schemas.AgentResponse)
def patch_agent(
    agent_id: int, 
    agent: AgentUpdate, 
    db: Session = Depends(get_db), 
    admin=Depends(get_current_admin)
):
    """
    Partially update an agent with only the fields that are provided.
    This allows updating individual fields without affecting others.
    """
    db_agent = db.query(models.Agent).filter(models.Agent.id == agent_id).first()
    if not db_agent:
        raise HTTPException(status_code=404, detail="Agent not found")
    
    # Check if updating name or role would conflict with existing agents
    if agent.name or agent.agent_role:
        filters = []
        if agent.name:
            filters.append(models.Agent.name == agent.name)
        if agent.agent_role:
            filters.append(models.Agent.agent_role == agent.agent_role)
        
        existing = db.query(models.Agent).filter(
            (models.Agent.id != agent_id) & 
            (filters[0] if len(filters) == 1 else (filters[0] | filters[1]))
        ).first()
        
        if existing:
            raise HTTPException(400, "Agent with this name or role already exists")
    
    # Update only the fields that were provided
    update_data = agent.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_agent, key, value)
    
    # Update the updated_at timestamp
    db_agent.updated_at = datetime.utcnow()
    
    db.commit()
    db.refresh(db_agent)
    return db_agent
