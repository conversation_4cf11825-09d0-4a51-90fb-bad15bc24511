"""Conversation management service"""
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import desc, update
from datetime import datetime

from app import models, crud
from app.schemas_files.voice_schemas import TranscriptMessage, ConversationSession
from app.exceptions.voice_exceptions import ConversationStorageException
from app.logger import app_logger as logger

class ConversationService:
    """Service for managing conversation storage and retrieval"""

    def store_conversation(
        self,
        db: Session,
        user_id: int,
        agent_role: str,
        transcript: List[Dict],
        language: str,
        metadata: Dict[str, Any] = None
    ) -> Optional[models.Conversation]:
        """Store conversation in the database"""
        try:
            if not transcript:
                logger.info("No transcript to store")
                return None

            # Use voice_assistant as default if agent_role is None
            effective_agent_role = agent_role if agent_role else "voice_assistant"

            # Get agent info if available
            agent = None
            if effective_agent_role:
                agent = crud.get_agent_by_role(db, effective_agent_role, language)

            # Create session data for this conversation
            current_session = self._create_session_data(transcript, metadata)

            # Check if there's an existing conversation for this user and agent
            existing_conversation = self._get_existing_conversation(
                db, user_id, effective_agent_role
            )

            if existing_conversation:
                return self._update_existing_conversation(
                    db, existing_conversation, current_session, agent, effective_agent_role
                )
            else:
                return self._create_new_conversation(
                    db, user_id, effective_agent_role, current_session, agent
                )

        except Exception as e:
            logger.error(f"Failed to store conversation: {e}", exc_info=True)
            db.rollback()
            raise ConversationStorageException(f"Failed to store conversation: {e}")

    def _create_session_data(
        self,
        transcript: List[Dict],
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create session data structure"""
        # Get timestamps from transcript
        start_time = None
        if transcript and "timestamp" in transcript[0]:
            start_time = transcript[0]["timestamp"]
        else:
            start_time = datetime.utcnow().isoformat()

        end_time = datetime.utcnow().isoformat()

        return {
            "messages": transcript,
            "started_at": start_time,
            "ended_at": end_time,
            "metadata": metadata or {"source": "voice_server"},
            "tokens_used": 0  # Set to 0 for now
        }

    def _get_existing_conversation(
        self,
        db: Session,
        user_id: int,
        agent_role: str
    ) -> Optional[models.Conversation]:
        """Get existing conversation for user and agent"""
        return db.query(models.Conversation).filter(
            models.Conversation.user_id == user_id,
            models.Conversation.agent_role == agent_role
        ).order_by(desc(models.Conversation.updated_at)).first()

    def _update_existing_conversation(
        self,
        db: Session,
        existing_conversation: models.Conversation,
        current_session: Dict[str, Any],
        agent,
        effective_agent_role: str
    ) -> models.Conversation:
        """Update existing conversation with new session"""
        logger.info(f"Updating existing conversation {existing_conversation.id}")

        # Get existing conversation data
        conversation_data = existing_conversation.conversation_data

        # Ensure conversation_data is a dictionary
        if not isinstance(conversation_data, dict):
            conversation_data = {
                "agent": {
                    "name": agent.name if agent else "Voice Assistant",
                    "role": effective_agent_role
                },
                "sessions": []
            }

        # Convert old format to new session-based format if needed
        if "sessions" not in conversation_data:
            conversation_data = self._convert_to_session_format(
                conversation_data, existing_conversation, agent, effective_agent_role
            )

        # Ensure sessions is a list
        if not isinstance(conversation_data.get("sessions"), list):
            conversation_data["sessions"] = []

        # Append the new session
        sessions = conversation_data.get("sessions", [])
        sessions.append(current_session)
        conversation_data["sessions"] = sessions

        # Update the conversation with the new data
        stmt = update(models.Conversation).where(
            models.Conversation.id == existing_conversation.id
        ).values(
            conversation_data=conversation_data,
            updated_at=datetime.utcnow()
        )
        db.execute(stmt)
        db.commit()

        # Refresh to get the updated data
        db.refresh(existing_conversation)

        return existing_conversation

    def _convert_to_session_format(
        self,
        conversation_data: Dict[str, Any],
        existing_conversation: models.Conversation,
        agent,
        effective_agent_role: str
    ) -> Dict[str, Any]:
        """Convert old conversation format to new session-based format"""
        if "messages" in conversation_data:
            old_messages = conversation_data.get("messages", [])
            old_session = {
                "messages": old_messages,
                "started_at": (
                    old_messages[0].get("timestamp", existing_conversation.created_at.isoformat())
                    if old_messages else existing_conversation.created_at.isoformat()
                ),
                "ended_at": existing_conversation.updated_at.isoformat(),
                "metadata": conversation_data.get("metadata", {}),
                "tokens_used": conversation_data.get("tokens_used", 0)
            }
            return {
                "agent": conversation_data.get("agent", {
                    "name": agent.name if agent else "Voice Assistant",
                    "role": effective_agent_role
                }),
                "sessions": [old_session]
            }
        else:
            # Just create a new sessions array
            return {
                "agent": {
                    "name": agent.name if agent else "Voice Assistant",
                    "role": effective_agent_role
                },
                "sessions": []
            }

    def _create_new_conversation(
        self,
        db: Session,
        user_id: int,
        effective_agent_role: str,
        current_session: Dict[str, Any],
        agent
    ) -> models.Conversation:
        """Create new conversation"""
        logger.info(f"Creating new conversation for user {user_id}")

        conversation_data = {
            "agent": {
                "name": agent.name if agent else "Voice Assistant",
                "role": effective_agent_role
            },
            "sessions": [current_session]
        }

        new_conversation = models.Conversation(
            user_id=user_id,
            agent_role=effective_agent_role,
            conversation_data=conversation_data
        )
        db.add(new_conversation)

        db.commit()
        db.refresh(new_conversation)

        return new_conversation

    def add_placeholder_messages(self, transcript: List[Dict]) -> List[Dict]:
        """Add placeholder assistant messages if missing"""
        # COMMENTED OUT: User requested to disable placeholder messages
        # user_messages = [msg for msg in transcript if msg.get("role") == "user"]
        # assistant_messages = [msg for msg in transcript if msg.get("role") == "assistant"]

        # if len(user_messages) <= len(assistant_messages):
        #     return transcript

        # logger.warning(f"Missing assistant messages: {len(user_messages) - len(assistant_messages)}")

        # # Add placeholder assistant messages
        # for i in range(len(assistant_messages), len(user_messages)):
        #     if i == 0:
        #         # First message placeholder
        #         placeholder_content = "Hello! How can I help you today?"
        #     else:
        #         # Subsequent message placeholder
        #         placeholder_content = "I understand. Is there anything else you'd like to know?"

        #     transcript.append({
        #         "role": "assistant",
        #         "content": placeholder_content,
        #         "timestamp": datetime.utcnow().isoformat(),
        #         "is_placeholder": True
        #     })

        # # Sort transcript by timestamp
        # transcript.sort(key=lambda x: x["timestamp"])

        # Return transcript unchanged
        return transcript

    def validate_transcript(self, transcript: List[Dict]) -> bool:
        """Validate transcript structure"""
        if not transcript:
            return False

        for msg in transcript:
            if not isinstance(msg, dict):
                return False
            if "role" not in msg or "content" not in msg:
                return False
            if msg["role"] not in ["user", "assistant"]:
                return False

        return True
