"""Voice service configuration"""
import os
from dotenv import load_dotenv

load_dotenv()

class VoiceConfig:
    """Configuration for voice services"""
    
    # Azure Assistant IDs
    PPCA_CA_ASSISTANT_ID = os.getenv("PPCA_CA_ASSISTANT_ID")
    MOA_ASSISTANT_ID = os.getenv("MOA_ASSISTANT_ID")
    DE_PPCA_CA_ASSISTANT_ID = os.getenv("DE_PPCA_CA_ASSISTANT_ID")
    DE_MOA_ASSISTANT_ID = os.getenv("DE_MOA_ASSISTANT_ID")
    
    # WebSocket settings
    INACTIVITY_TIMEOUT = 15  # seconds
    MAX_FALLBACKS = 2
    
    # Audio settings
    AUDIO_FORMAT = "pcm16"
    VOICE_MODELS = {
        "female": "sage",
        "male": "echo",
        "default": "alloy"
    }
    
    # Session settings
    TURN_DETECTION = {
        "type": "server_vad",
        "threshold": 0.5,
        "prefix_padding_ms": 300,
        "silence_duration_ms": 1500,  # Increased from 200ms to 1500ms
        "create_response": True,
        "interrupt_response": True
    }
    # TURN_DETECTION = {
    # "type": "semantic_vad",
    # "threshold": 0.5,  # Confidence threshold for speech completion
    # "prefix_padding_ms": 300,
    # "silence_duration_ms": 1500,  # Still useful as a fallback timeout
    # "create_response": True,
    # "interrupt_response": True
    # }
