
from datetime import datetime, date


def calculate_age(birthdate):
    """
    Calculate age from birthdate

    Args:
        birthdate: datetime object representing user's date of birth (can be None)

    Returns:
        int: Age in years, or None if birthdate is None
    """
    if birthdate is None:
        return None

    if isinstance(birthdate, str):
        try:
            birthdate = datetime.strptime(birthdate, "%Y-%m-%d %H:%M:%S.%f")
        except ValueError:
            try:
                birthdate = datetime.strptime(birthdate, "%Y-%m-%d")
            except ValueError:
                raise ValueError("Invalid birthdate format")

    today = date.today()
    return today.year - birthdate.year - ((today.month, today.day) < (birthdate.month, birthdate.day))

