from passlib.context import Crypt<PERSON>ontext
from jose import <PERSON><PERSON><PERSON><PERSON><PERSON>, jwt
from datetime import datetime, timedelta
from fastapi import Depends, HTTPException, status, WebSocket
from fastapi.security import OAuth2<PERSON>asswordBearer
from sqlalchemy.orm import Session, joinedload
from . import crud, schemas
from app import models
import os
from decouple import config
from app.database import get_db



SECRET_KEY = config('JWT_SECRET_KEY')
ALGORITHM = 'HS256'
ACCESS_TOKEN_EXPIRE_MINUTES = 60

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/login")

def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    expire = datetime.utcnow() + (expires_delta or timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES))
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

def blacklist_token(db: Session, token: str):
    """Add a token to the blacklist"""
    blacklisted = models.BlacklistedToken(token=token)
    db.add(blacklisted)
    db.commit()

def is_token_blacklisted(db: Session, token: str) -> bool:
    """Check if a token is blacklisted"""
    return db.query(models.BlacklistedToken).filter(
        models.BlacklistedToken.token == token
    ).first() is not None

def verify_token(token: str) -> dict:
    """
    Verify a JWT token and return its payload
    
    Args:
        token: The JWT token to verify
        
    Returns:
        dict: The decoded token payload
        
    Raises:
        JWTError: If the token is invalid or expired
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError:
        raise JWTError("Invalid or expired token")

async def verify_token_and_get_user(token: str, db: Session) -> models.User:
    """Common authentication logic for both HTTP and WebSocket"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    # Check if token is blacklisted
    if is_token_blacklisted(db, token):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has been revoked",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email: str = payload.get("sub")
        if email is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
        
    user = crud.get_user_by_email(db, email=email)
    if user is None:
        raise credentials_exception
    return user

# Keep the original get_current_user unchanged to maintain compatibility
async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
) -> models.User:
    """For HTTP routes - maintains original behavior"""
    return await verify_token_and_get_user(token, db)

async def get_current_user_ws(websocket: WebSocket) -> models.User:
    """For WebSocket routes only"""
    token = websocket.query_params.get("token")
    if not token or token == "undefined":
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing or invalid token",
        )
    
    try:
        # First verify the token without database
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email: str = payload.get("sub")
        if email is None:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token payload",
            )
        
        # Get database session
        db = next(get_db())
        
        # Check if token is blacklisted
        if is_token_blacklisted(db, token):
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has been revoked",
            )
            
        # Get user with eager loading of details
        user = db.query(models.User).options(
            joinedload(models.User.details)
        ).filter(models.User.email == email).first()
        
        if user is None:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found",
            )
        
        # Check if user is approved
        if not user.is_approved:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Account pending approval. Please wait for admin approval.",
            )
            
        return user
        
    except JWTError:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
        )

async def get_approved_user(
    current_user: models.User = Depends(get_current_user)
) -> models.User:
    """
    Dependency to check if user is approved.
    Use this instead of get_current_user when you want to ensure user is approved.
    """
    if not current_user.is_approved:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Account pending approval. Please wait for admin approval."
        )
    return current_user

# Exception for auth-related endpoints that don't require approval
APPROVAL_EXEMPT_ENDPOINTS = {
    "/auth/login",
    "/auth/signup",
    "/auth/logout",
    "/users/me"  # Optionally allow users to see their own status
}

async def get_current_user_with_approval_check(
    request_path: str,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
) -> models.User:
    """
    Enhanced version of get_current_user that checks approval status
    except for exempt endpoints
    """
    user = await verify_token_and_get_user(token, db)
    
    if request_path not in APPROVAL_EXEMPT_ENDPOINTS and not user.is_approved:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Account pending approval. Please wait for admin approval."
        )
    
    return user
