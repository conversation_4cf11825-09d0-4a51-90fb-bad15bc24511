from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional, List
from datetime import datetime

from app.database import get_db
from app.auth import get_current_user
from app.routers.admin import get_current_admin
from app import models, schemas, crud

router = APIRouter(prefix="/frontend-logs", tags=["frontend-logs"])


@router.post("/", response_model=schemas.FrontendLogResponse)
def create_frontend_log(
    log: schemas.FrontendLogCreate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Create a new frontend log entry
    """
    return crud.create_frontend_log(
        db=db,
        user_id=current_user.id,
        log_data=log.log_data,
        log_type=log.log_type
    )


@router.post("/batch", response_model=List[schemas.FrontendLogResponse])
def create_frontend_logs_batch(
    batch_logs: schemas.FrontendLogBatchCreate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Create multiple frontend log entries in batch
    """
    # Convert Pydantic models to dict for CRUD function
    logs_data = []
    for log in batch_logs.logs:
        logs_data.append({
            'log_data': log.log_data,
            'log_type': log.log_type
        })
    
    return crud.create_frontend_logs_batch(
        db=db,
        user_id=current_user.id,
        logs=logs_data
    )


@router.get("/", response_model=List[schemas.FrontendLogResponse])
def get_frontend_logs(
    limit: int = Query(30, ge=1, le=100),
    user_id: Optional[int] = Query(None),
    log_type: Optional[str] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_db),
    admin = Depends(get_current_admin)
):
    """
    Get frontend log entries (admin only)
    """
    return crud.get_all_frontend_logs(
        db=db,
        limit=limit,
        user_id=user_id,
        log_type=log_type,
        start_date=start_date,
        end_date=end_date
    )


@router.get("/my-logs", response_model=List[schemas.FrontendLogResponse])
def get_my_frontend_logs(
    limit: int = Query(30, ge=1, le=100),
    log_type: Optional[str] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get current user's frontend log entries
    """
    return crud.get_frontend_logs_by_user(
        db=db,
        user_id=current_user.id,
        limit=limit,
        log_type=log_type,
        start_date=start_date,
        end_date=end_date
    )


@router.get("/{log_id}", response_model=schemas.FrontendLogResponse)
def get_frontend_log(
    log_id: int,
    db: Session = Depends(get_db),
    admin = Depends(get_current_admin)
):
    """
    Get a specific frontend log entry by ID (admin only)
    """
    log = crud.get_frontend_log_by_id(db, log_id)
    if not log:
        raise HTTPException(status_code=404, detail="Frontend log not found")
    return log
