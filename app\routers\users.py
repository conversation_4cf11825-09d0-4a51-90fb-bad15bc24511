from fastapi import APIRouter, Depends, Request, HTTPException, Form
from sqlalchemy.orm import Session
from .. import schemas, crud, models
from ..database import get_db
from ..auth import get_current_user_with_approval_check, oauth2_scheme,get_current_user
from typing import Annotated, Optional
from ..logger import app_logger as logger
from datetime import datetime

router = APIRouter(prefix="/users", tags=["users"])

async def get_current_user_me(
    token: Annotated[str, Depends(oauth2_scheme)],
    db: Session = Depends(get_db)
) -> models.User:
    return await get_current_user_with_approval_check("/users/me", token, db)

@router.get('/me', response_model=schemas.UserOut)
async def read_users_me(
    current_user: models.User = Depends(get_current_user)
):
    """Get current user profile"""
    logger.debug(f"User profile accessed: {current_user.email}")
    return current_user

@router.put('/me', response_model=schemas.UserOut)
async def update_user_me(
    user_update: schemas.UserUpdate,
    current_user: models.User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update user information using the UserUpdate schema
    which allows updating only specific fields
    """
    try:
        # Update details if provided
        if user_update.details:
            if not current_user.details:
                # Only create details if we have the required fields
                details_data = user_update.details.dict(exclude_unset=True)
                required_fields = ['first_name', 'last_name', 'country_code', 'mobile_number']

                # Check if all required fields are provided
                if all(field in details_data and details_data[field] is not None for field in required_fields):
                    # Create details with all provided data
                    current_user.details = models.UserDetails(user_id=current_user.id, **details_data)
                else:
                    # If not all required fields are provided, raise an error
                    missing_fields = [field for field in required_fields if field not in details_data or details_data[field] is None]
                    raise HTTPException(
                        status_code=400,
                        detail=f"Cannot create user details. Missing required fields: {', '.join(missing_fields)}"
                    )
            else:
                # Update existing details
                details_data = user_update.details.dict(exclude_unset=True)
                for key, value in details_data.items():
                    if value is not None:  # Only update if value is provided
                        setattr(current_user.details, key, value)

        # Allow users to update their remaining_time
        if user_update.remaining_time is not None:
            current_user.remaining_time = user_update.remaining_time

        # Update the updated_at timestamp
        current_user.updated_at = datetime.utcnow()

        # Commit changes
        db.commit()
        db.refresh(current_user)  # Log the update
        logger.info(f"User updated: {current_user.email}")

        # Return the updated user
        return current_user
    except Exception as e:
        # Log the error
        logger.error(f"Error updating user: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error updating user: {str(e)}")

@router.post("/me", response_model=schemas.UserOut)
async def update_current_user_via_post(
    method: Optional[str] = Form(None),
    remaining_time: Optional[int] = Form(None),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Special endpoint to handle POST requests from navigator.sendBeacon that should be treated as PUT
    """
    if method != "PUT":
        raise HTTPException(status_code=400, detail="This endpoint only supports POST requests with method=PUT")

    # Only update remaining_time if provided
    if remaining_time is not None:
        current_user.remaining_time = remaining_time
        current_user.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(current_user)

    return current_user
