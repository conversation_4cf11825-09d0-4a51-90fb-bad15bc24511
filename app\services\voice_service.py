"""Main voice service orchestrator"""
import asyncio
from sqlalchemy.orm import Session

from app import models
from app.auth import get_current_user_ws
from app.utils import calculate_age
from app.services.azure_service import AzureWebSocketService
from app.services.agent_service import AgentInstructionService
from app.services.conversation_service import ConversationService
from app.schemas_files.voice_schemas import AgentContext
from app.exceptions.voice_exceptions import VoiceServiceException
from app.logger import app_logger as logger

class VoiceService:
    """Main service for orchestrating voice interactions"""

    def __init__(self):
        self.azure_service = AzureWebSocketService()
        self.agent_service = AgentInstructionService()
        self.conversation_service = ConversationService()

    async def authenticate_user(self, websocket) -> models.User:
        """Authenticate user from WebSocket"""
        try:
            user = await get_current_user_ws(websocket)
            logger.info(f"WebSocket connection authenticated for user: {user.email}")
            return user
        except Exception as e:
            logger.error(f"Authentication failed: {e}", exc_info=True)
            raise VoiceServiceException(f"Authentication failed: {e}")

    def create_agent_context(
        self,
        user: models.User,
        agent_role: str,
        language: str
    ) -> AgentContext:
        """Create agent context from user and parameters"""
        user_age = None
        user_first_name = None
        user_last_name = None
        user_birthdate = None
        user_org_name = None
        user_phone_number = None

        if hasattr(user, 'details') and user.details:
            if user.details.birthdate:
                user_age = calculate_age(user.details.birthdate)
                user_birthdate = user.details.birthdate.isoformat() if user.details.birthdate else None
            user_first_name = user.details.first_name
            user_last_name = user.details.last_name
            user_org_name = user.details.org_name
            user_phone_number = user.details.mobile_number

        return AgentContext(
            user_id=user.id,
            agent_role=agent_role,
            language=language,
            user_age=user_age,
            user_first_name=user_first_name,
            user_last_name=user_last_name,
            user_birthdate=user_birthdate,
            user_org_name=user_org_name,
            user_phone_number=user_phone_number
        )

    async def get_agent_instructions(
        self,
        db: Session,
        context: AgentContext,
        for_audio_text: bool = False
    ) -> str:
        """Get agent instructions for the given context"""
        return await self.agent_service.get_agent_instructions(db, context, for_audio_text)

    def get_voice_model(self, user: models.User) -> str:
        """Get appropriate voice model for user"""
        user_gender = None
        if hasattr(user, 'details') and user.details and user.details.avatar_gender:
            user_gender = user.details.avatar_gender

        return self.azure_service.get_voice_model(user_gender)

    async def setup_azure_connection(self, db: Session):
        """Setup Azure WebSocket connection"""
        api_key, url = await self.azure_service.get_azure_config(db)
        if not api_key or not url:
            raise VoiceServiceException("Azure configuration missing from database")

        headers = self.azure_service.create_azure_headers(api_key)
        azure_ws = await self.azure_service.connect_to_azure(url, headers)

        return azure_ws, headers

    async def configure_session(
        self,
        azure_ws,
        instructions: str,
        voice_model: str,
        language: str = "en"
    ):
        """Configure Azure session"""
        session_config = self.azure_service.create_session_config(instructions, voice_model, language)
        await self.azure_service.send_session_config(azure_ws, session_config)

    def get_initial_message(
        self,
        agent_role: str,
        language: str,
        context: AgentContext,
        moa_instructions: str = ""
    ) -> str:
        """Get initial message for conversation"""

        # Build user context based on agent role and language
        if language == "de":
            age_text = f" ist {context.user_age} Jahre alt" if context.user_age is not None else ""
            user_context_base = (
                f"Kontext zum Nutzer: {context.user_first_name}{age_text} "
                f"hat Demenz im frühen bis mittleren Stadium. Grüße {context.user_first_name} "
                f"freundlich und erkläre was du nun mit dem Nutzer vor hast."
            )
            # Add onboarding instruction only for OBA agent
            if agent_role == "oba":
                user_context = user_context_base + " Dann beginne mit dem Kennlerngespräch."
            else:
                user_context = user_context_base
        else:
            age_text = f" is {context.user_age} years old" if context.user_age is not None else ""
            user_context_base = (
                f"Context about the user: {context.user_first_name}{age_text} "
                f"and potentially has early stage dementia. Greet {context.user_first_name} "
                f"kindly, introduce yourself and what you are going to do with {context.user_first_name} "
                f"now."
            )
            # Add onboarding instruction only for OBA agent
            if agent_role == "oba":
                user_context = user_context_base + " Then begin with the onboarding conversation."
            else:
                user_context = user_context_base

        logger.info(f"user_context_base: {agent_role}")

        logger.info(f"user_context: {user_context}")
        return self.agent_service.get_initial_message(agent_role, language, moa_instructions, user_context)

    def store_conversation(
        self,
        db: Session,
        user_id: int,
        agent_role: str,
        transcript: list,
        language: str,
        metadata: dict = None
    ):
        """Store conversation in database"""
        # Validate and add placeholder messages if needed
        if not self.conversation_service.validate_transcript(transcript):
            logger.warning("Invalid transcript structure")
            return None

        # Add placeholder messages if needed
        transcript = self.conversation_service.add_placeholder_messages(transcript)

        # Handle cm_fa conversations differently - store in CommonFeedback table
        if agent_role == "cm_fa":
            return self.store_common_feedback(db, user_id, transcript, language, metadata)
        else:
            return self.conversation_service.store_conversation(
                db, user_id, agent_role, transcript, language, metadata
            )

    def store_common_feedback(
        self,
        db: Session,
        user_id: int,
        transcript: list,
        language: str,
        metadata: dict = None
    ):
        """Store cm_fa conversation in CommonFeedback table"""
        try:
            from app import crud

            # Create session data for this feedback conversation
            current_session = self.conversation_service._create_session_data(transcript, metadata)

            conversation_data = {
                "agent": {
                    "name": "Common Feedback Assistant",
                    "role": "cm_fa"
                },
                "sessions": [current_session],
                "language": language
            }

            # Store in CommonFeedback table
            feedback_record = crud.create_common_feedback(
                db, user_id, conversation_data
            )

            logger.info(f"Stored common feedback for user {user_id}")
            return feedback_record

        except Exception as e:
            logger.error(f"Failed to store common feedback: {e}", exc_info=True)
            db.rollback()
            return None

    async def store_ppca_instructions_async(
        self,
        db: Session,
        user_id: int,
        agent_role: str,
        language: str,
        conversation_data: str
    ):
        """Store PPCA instructions asynchronously"""
        try:
            await self.agent_service.store_ppca_instructions(
                db, user_id, agent_role, language, conversation_data
            )
        except Exception as e:
            logger.error(f"Error storing PPCA instructions: {e}", exc_info=True)

    def should_store_ppca_instructions(self, agent_role: str) -> bool:
        """Check if PPCA instructions should be stored for this agent role"""
        return agent_role == "oba"

    def create_ppca_task(
        self,
        db: Session,
        user_id: int,
        language: str,
        transcript: list
    ):
        """Create async task for storing PPCA instructions"""
        if not transcript:
            return

        main_transcript = "<transcript_start>" + str(transcript) + "</transcript_end>"

        # Create a task that runs independently
        return asyncio.create_task(
            self.store_ppca_instructions_async(
                db, user_id, "ppca", language, main_transcript
            )
        )
