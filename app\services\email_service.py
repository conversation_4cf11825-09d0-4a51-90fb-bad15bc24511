"""Email service for sending notifications and communications"""
import os
from typing import Optional
from fastapi_mail import FastMail, MessageSchema, ConnectionConfig
from dotenv import load_dotenv
from app.logger import app_logger as logger

# Load environment variables
load_dotenv()

def normalize_text(text: str) -> str:
    """Replace non-breaking spaces and other problematic characters with their ASCII equivalents."""
    return text.replace('\xa0', ' ').replace('\u200b', '')  # Add other problematic chars if needed

class EmailService:
    """Service for handling email communications"""
    
    def __init__(self):
        # Email configuration from environment variables
        self.mail_username = normalize_text(os.getenv("MAIL_USERNAME"))
        self.mail_password = normalize_text(os.getenv("MAIL_PASSWORD"))
        self.mail_from = normalize_text(os.getenv("MAIL_FROM"))
        self.admin_notification_email = normalize_text(os.getenv("ADMIN_NOTIFICATION_EMAIL"))
        self.frontend_url = os.getenv("FRONTEND_URL", "http://localhost:3000")
        
        # Validate required configuration
        if not all([self.mail_username, self.mail_password, self.mail_from]):
            logger.warning("Email configuration incomplete. Some email features may not work.")
    
    def _get_connection_config(self) -> ConnectionConfig:
        """Get FastMail connection configuration"""
        return ConnectionConfig(
            MAIL_USERNAME=self.mail_username,
            MAIL_PASSWORD=self.mail_password,
            MAIL_FROM=self.mail_from,
            MAIL_PORT=587,
            MAIL_SERVER="smtp.gmail.com",
            MAIL_STARTTLS=True,
            MAIL_SSL_TLS=False,
            USE_CREDENTIALS=True
        )
    
    async def send_admin_notification_email(self, user_email: str, user_first_name: str) -> bool:
        """
        Send email notification to admin when a new user signs up
        
        Args:
            user_email: Email address of the new user
            user_first_name: First name of the new user
            
        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        if not self.admin_notification_email:
            logger.warning("ADMIN_NOTIFICATION_EMAIL not configured, skipping admin notification")
            return False
        
        if not all([self.mail_username, self.mail_password, self.mail_from]):
            logger.error("Email configuration incomplete, cannot send admin notification")
            return False
        
        try:
            conf = self._get_connection_config()
            
            # Create message with the exact template requested
            message = MessageSchema(
                subject="New User Sign-up - Luma Life",
                recipients=[self.admin_notification_email],
                body=f"""
                <html>
                <body>
                    <p>Hi team Luma Life,</p>
                    <p>You have got a new sign-up.</p>
                    <p><strong>Name:</strong> {user_first_name}</p>
                    <p><strong>Email:</strong> {user_email}</p>
                    <p>Please confirm their access via the admin portal.</p>
                    <p>Best,<br>Luma Service Bot</p>
                </body>
                </html>
                """,
                subtype="html"
            )
            
            # Send email
            fm = FastMail(conf)
            await fm.send_message(message)
            logger.info(f"Admin notification email sent for new user: {user_email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send admin notification email for user {user_email}: {str(e)}")
            return False
    
    async def send_password_reset_email(self, user_email: str, user_first_name: str, reset_token: str) -> bool:
        """
        Send password reset email to user
        
        Args:
            user_email: Email address of the user
            user_first_name: First name of the user
            reset_token: Password reset token
            
        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        if not all([self.mail_username, self.mail_password, self.mail_from]):
            logger.error("Email configuration incomplete, cannot send password reset email")
            return False
        
        try:
            conf = self._get_connection_config()
            
            # Create password reset URL
            reset_url = f"{self.frontend_url}/reset-password?token={reset_token}"
            
            message = MessageSchema(
                subject="Password Reset Request",
                recipients=[user_email],
                body=f"""
                <html>
                <body>
                    <p>Hi {user_first_name},</p>
                    <p>You requested a password reset. Please click the link below to reset your password:</p>
                    <p><a href="{reset_url}">Reset Password</a></p>
                    <p>This link will expire in 15 minutes.</p>
                    <p>If you didn't request this, please ignore this email.</p>
                </body>
                </html>
                """,
                subtype="html"
            )
            
            # Send email
            fm = FastMail(conf)
            await fm.send_message(message)
            logger.info(f"Password reset email sent to: {user_email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send password reset email to {user_email}: {str(e)}")
            return False

# Create a singleton instance
email_service = EmailService()
