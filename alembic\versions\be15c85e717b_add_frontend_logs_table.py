"""add frontend_logs table

Revision ID: be15c85e717b
Revises: 
Create Date: 2025-07-03 18:05:47.975985

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'be15c85e717b'
down_revision: Union[str, None] = '123456789abc'  # <-- set to users table migration revision
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade() -> None:
    op.create_table(
        'frontend_logs',
        sa.Column('id', sa.Integer(), primary_key=True, index=True),
        sa.Column('user_id', sa.Integer(), sa.<PERSON>ey('users.id'), nullable=False),
        sa.Column('log_data', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('log_type', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
    )

def downgrade() -> None:
    op.drop_table('frontend_logs')