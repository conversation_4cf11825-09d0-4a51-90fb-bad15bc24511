import logging
import sys
from pathlib import Path
from logging.handlers import RotatingFileH<PERSON><PERSON>

def setup_logger(name, log_file=None, level=logging.INFO, max_size_mb=10, backup_count=5):
    """
    Set up a logger with console and optional file output with size limits
    
    Args:
        name: Logger name
        log_file: Optional path to log file
        level: Logging level
        max_size_mb: Maximum size of log file in MB before rotation
        backup_count: Number of backup files to keep
        
    Returns:
        Logger instance
    """
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Clear existing handlers to avoid duplicates when reloading
    if logger.hasHandlers():
        logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # Create file handler if log_file is provided
    if log_file:
        # Ensure log directory exists
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Use RotatingFileHandler instead of FileHandler
        max_bytes = max_size_mb * 1024 * 1024  # Convert MB to bytes
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

# Create default application logger
app_logger = setup_logger(
    "lumalife", 
    log_file="logs/app.log", 
    max_size_mb=10,  # 10MB per file
    backup_count=5   # Keep 5 backup files
)
