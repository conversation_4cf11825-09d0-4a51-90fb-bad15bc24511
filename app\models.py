from sqlalchemy.orm import relationship
from .database import Base
import enum
from datetime import datetime
from sqlalchemy import <PERSON>um<PERSON>, Integer, String, <PERSON>olean, Foreign<PERSON>ey, DateTime, Enum, Text, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import JSONB, ARRAY

class RoleEnum(str, enum.Enum):
    user = 'user'
    admin = 'admin'

class UserStageEnum(int, enum.Enum):
    BLOCKED = 0      # User account is blocked/deactivated
    NEW = 1          # Initial state after registration
    AVATAR_SELECTION = 2  # User selecting their preferred avatar gender
    ONBOARDING = 3   # User going through initial onboarding with OBA
    PPCA_CREATION = 4  # User configuring their Personal Care Assistant
    CONVERSATION = 5 # User in active conversation with <PERSON><PERSON>
    FEEDBACK = 6     # User providing feedback about experience
    REGULAR = 7      # User in normal usage state, all stages completed

class AgentTypeEnum(int, enum.Enum):
    OBA = 1          # Onboarding Assistant - handles initial user setup
    PPCA_CONFIG = 2  # PPCA Configuration - handles personal care assistant setup
    PPCA = 3         # Personal Care Assistant - handles main conversations


class Conversation(Base):
    __tablename__ = "conversations"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    agent_role = Column(String, nullable=False)  # Store roles like "onboarding_agent", "oa", etc.
    conversation_data = Column(JSONB, nullable=False, default=list)
    created_at = Column(DateTime(), nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime(), nullable=False, default=datetime.utcnow)

    # Add this relationship
    user = relationship("User", back_populates="conversations")


class CommonFeedback(Base):
    __tablename__ = "common_feedbacks"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    conversation_data = Column(JSONB, nullable=False, default=list)  # Store the cm_fa conversation
    created_at = Column(DateTime(), nullable=False, default=datetime.utcnow)

    # Relationship
    user = relationship("User", back_populates="common_feedbacks")

class FrontendLog(Base):
    __tablename__ = "frontend_logs"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    log_data = Column(JSONB, nullable=False)  # Store logs/metadata as-is from frontend
    log_type = Column(String, nullable=True)  # Optional: error, info, debug, etc.
    created_at = Column(DateTime(), nullable=False, default=datetime.utcnow)

    # Relationship
    user = relationship("User", back_populates="frontend_logs")


class BlacklistedToken(Base):
    __tablename__ = "blacklisted_tokens"

    token = Column(String, primary_key=True)
    blacklisted_on = Column(DateTime, default=datetime.utcnow)


class Agent(Base):
    __tablename__ = "agents"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    agent_role = Column(String, nullable=False)
    instructions = Column(Text, nullable=False)
    language = Column(String, nullable=False, default="en")  # Add language column with default
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow)

class Assistant(Base):
    __tablename__ = "assistants"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    agent_role = Column(String, nullable=False)  # Changed from agent_id to agent_role
    instructions = Column(Text, nullable=False)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="assistants")
    # Remove the agent relationship since we're using agent_role instead of agent_id

class Configuration(Base):
    __tablename__ = "configurations"
    
    id = Column(Integer, primary_key=True, index=True)
    key = Column(String, nullable=False, unique=True)
    value = Column(JSONB, nullable=False)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow)

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    role = Column(SQLEnum(RoleEnum), default=RoleEnum.user)
    is_approved = Column(Boolean, default=False)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    current_stage = Column(Integer, nullable=False, default=UserStageEnum.NEW.value)
    max_token_limit = Column(Integer, default=0)
    tokens_used = Column(Integer, default=0)
    remaining_time = Column(Integer, default=3600)

    # Relationships
    details = relationship("UserDetails", back_populates="user", uselist=False)
    assistants = relationship("Assistant", back_populates="user")
    conversations = relationship("Conversation", back_populates="user")
    common_feedbacks = relationship("CommonFeedback", back_populates="user")
    frontend_logs = relationship("FrontendLog", back_populates="user")

    @property
    def tokens_remaining(self):
        """Get remaining available tokens"""
        return max(0, self.max_token_limit - self.tokens_used)

    @property
    def has_available_tokens(self):
        """Check if user has tokens available"""
        return self.tokens_used < self.max_token_limit

    @property
    def stage(self):
        """Get the UserStageEnum object from the stored integer value"""
        return UserStageEnum(self.current_stage)

    @stage.setter
    def stage(self, new_stage: UserStageEnum):
        """Set the stage using either enum or integer"""
        if isinstance(new_stage, UserStageEnum):
            self.current_stage = new_stage.value
        else:
            self.current_stage = int(new_stage)

class UserDetails(Base):
    __tablename__ = "user_details"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), unique=True, nullable=False)
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    country_code = Column(String, nullable=False)
    mobile_number = Column(String, nullable=False)
    org_name = Column(String, nullable=True)
    avatar_gender = Column(Enum('male','female', name='gender_enum'), nullable=False, default='female')
    birthdate = Column(DateTime, nullable=True)
    language = Column(String, nullable=False, default="en")  # Add language column with default
    
    # Relationship
    user = relationship("User", back_populates="details")
