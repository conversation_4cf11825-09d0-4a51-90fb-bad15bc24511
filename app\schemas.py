from pydantic import BaseModel, ConfigDict, EmailStr
from datetime import datetime
from typing import Optional, List, Dict, Any
from .models import AgentType<PERSON><PERSON>, RoleEnum, UserStageEnum

# Token schemas
class Token(BaseModel):
    model_config = ConfigDict(extra='ignore')
    access_token: str
    token_type: str

class TokenData(BaseModel):
    model_config = ConfigDict(extra='ignore')
    email: Optional[str] = None

# User related schemas
class UserDetailsBase(BaseModel):
    model_config = ConfigDict(extra='ignore')
    first_name: str
    last_name: str
    country_code: str
    mobile_number: str
    avatar_gender: str
    birthdate: Optional[datetime] = None
    org_name: Optional[str] = None
    language: Optional[str] = "en"  # Add language field with default

class UserBase(BaseModel):
    model_config = ConfigDict(extra='ignore')
    email: EmailStr

class UserCreate(UserBase):
    password: str
    details: UserDetailsBase

class UserDetailsOut(UserDetailsBase):
    model_config = ConfigDict(from_attributes=True, extra='ignore')
    id: int
    user_id: int

class UserOut(UserBase):
    model_config = ConfigDict(from_attributes=True, extra='ignore')
    id: int
    is_approved: bool
    role: RoleEnum
    current_stage: UserStageEnum
    created_at: datetime
    max_token_limit: int
    tokens_used: int
    tokens_remaining: int
    remaining_time: int
    details: Optional[UserDetailsOut] = None

class UserDetailsUpdate(BaseModel):
    model_config = ConfigDict(extra='ignore')
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    avatar_gender: Optional[str] = None
    org_name: Optional[str] = None
    birthdate: Optional[datetime] = None
    language: Optional[str] = None  # Add language field

class UserUpdate(BaseModel):
    model_config = ConfigDict(extra='ignore')
    email: Optional[EmailStr] = None
    # Admin-only fields (these will be ignored in the /users/me endpoint)
    remaining_time: Optional[int] = None
    max_token_limit: Optional[int] = None
    # User-editable fields
    details: Optional[UserDetailsUpdate] = None

# Agent related schemas
class AgentBase(BaseModel):
    model_config = ConfigDict(extra='ignore')
    name: str
    instructions: str
    language: Optional[str] = "en"  # Add language field with default

class AgentCreate(AgentBase):
    agent_role: str  # Add the missing agent_role field

class AgentResponse(AgentBase):
    model_config = ConfigDict(from_attributes=True, extra='ignore')
    id: int
    agent_role: str
    language: str  # Include in response
    created_at: datetime
    updated_at: datetime

# Assistant related schemas
class AssistantBase(BaseModel):
    model_config = ConfigDict(extra='ignore')
    user_id: int
    agent_role: str  # Changed from agent_id to agent_role
    instructions: str

class AssistantCreate(AssistantBase):
    pass

class AssistantResponse(AssistantBase):
    model_config = ConfigDict(from_attributes=True, extra='ignore')
    id: int
    created_at: datetime
    updated_at: datetime

# Conversation related schemas
class SessionData(BaseModel):
    messages: List[Dict[str, Any]]
    started_at: datetime
    ended_at: datetime
    metadata: Optional[Dict[str, Any]] = None
    tokens_used: int

class ConversationData(BaseModel):
    model_config = ConfigDict(extra='ignore')
    agent_role: str  # Changed from agent_type to agent_role
    sessions: List[SessionData]

class ConversationCreate(BaseModel):
    model_config = ConfigDict(extra='ignore')
    agent_role: str  # Changed from agent_type to agent_role
    conversation_data: Dict[str, Any]
    metadata: Optional[Dict[str, Any]] = None

class ConversationResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True, extra='ignore')
    id: int
    user_id: int
    agent_role: str  # Changed from agent_type to agent_role
    conversation_data: Dict[str, Any]
    created_at: datetime
    updated_at: datetime

class ConversationFilter(BaseModel):
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    limit: Optional[int] = 30
    user_id: Optional[int] = None
    agent_role: Optional[str] = None  # Changed from agent_type to agent_role

# Common Feedback related schemas
class CommonFeedbackCreate(BaseModel):
    model_config = ConfigDict(extra='ignore')
    conversation_data: Dict[str, Any]

class CommonFeedbackResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True, extra='ignore')
    id: int
    user_id: int
    conversation_data: Dict[str, Any]
    created_at: datetime

class CommonFeedbackFilter(BaseModel):
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    limit: Optional[int] = 30
    user_id: Optional[int] = None

class UserFilter(BaseModel):
    model_config = ConfigDict(extra='ignore')
    is_approved: Optional[bool] = None
    role: Optional[RoleEnum] = None
    current_stage: Optional[UserStageEnum] = None
    email: Optional[str] = None

class UserList(BaseModel):
    model_config = ConfigDict(from_attributes=True, extra='ignore')
    id: int
    email: str
    is_approved: bool
    current_stage: UserStageEnum
    role: RoleEnum
    max_token_limit: int
    tokens_used: int
    tokens_remaining: int
    remaining_time: int  # Make sure this field is included
    created_at: datetime
    details: Optional[UserDetailsOut] = None

class TokenLimitUpdate(BaseModel):
    model_config = ConfigDict(extra='ignore')
    max_token_limit: int

class StageComplete(BaseModel):
    model_config = ConfigDict(extra='ignore')
    conversation_data: Dict[str, Any]

# Frontend Log related schemas
class FrontendLogCreate(BaseModel):
    model_config = ConfigDict(extra='ignore')
    log_data: Dict[str, Any]  # Store any data structure from frontend
    log_type: Optional[str] = None  # Optional: error, info, debug, warning, etc.

class FrontendLogBatchCreate(BaseModel):
    model_config = ConfigDict(extra='ignore')
    logs: List[FrontendLogCreate]  # Accept array of logs for batch creation

class FrontendLogResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True, extra='ignore')
    id: int
    user_id: int
    log_data: Dict[str, Any]
    log_type: Optional[str] = None
    created_at: datetime

class FrontendLogFilter(BaseModel):
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    limit: Optional[int] = 30
    user_id: Optional[int] = None
    log_type: Optional[str] = None
