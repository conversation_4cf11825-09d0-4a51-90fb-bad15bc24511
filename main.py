from fastapi import FastAP<PERSON>
from app.database import engine, Base, create_tables
from app.routers import auth, users, conversations, agents, user_journey, voice_server, admin, ppca_status, common_feedback, frontend_logs
from dotenv import load_dotenv
import os
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime, timezone
from app.logger import app_logger as logger


app = FastAPI(title="Lumalife AI-Assistant Backend",
    docs_url=None,
    redoc_url=None,
    openapi_url=None)

# Update CORS middleware with specific origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Include routers
app.include_router(auth.router)
app.include_router(users.router)
app.include_router(conversations.router)
app.include_router(agents.router)
app.include_router(user_journey.router)
app.include_router(voice_server.router)
app.include_router(admin.router)
app.include_router(ppca_status.router)
app.include_router(common_feedback.router)
app.include_router(frontend_logs.router)

@app.post("/init-db")
def initialize_database():
    create_tables()
    return {"message": "Database tables created successfully"}

@app.get("/health")
async def health_check():
    """
    Health check endpoint for the API
    """
    return {
        "status": "healthy",
        "service": "lumalife-backend",
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

@app.on_event("startup")
async def startup_event():
    logger.info("Application starting up")
    # You can add any startup tasks here

@app.on_event("shutdown")
async def shutdown_event():
    logger.info("Application shutting down")
    # You can add any cleanup tasks here

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
