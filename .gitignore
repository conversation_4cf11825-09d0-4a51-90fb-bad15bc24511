# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
.env
.venv
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Project specific
.env.local

# Logs
*.log
logs/

# Database
*.sqlite3
*.db

# Docker
.docker/
docker-compose.override.yml

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/
coverage.xml
*.cover
.hypothesis/

# Distribution / packaging
.Python
*.manifest
*.spec

# Jupyter Notebook
.ipynb_checkpoints

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Environments
.env.development
.env.test
.env.production
.env.staging

# Environment variables
.env
.env.local
.env.*.local

# Keep example env file
!.env.example
