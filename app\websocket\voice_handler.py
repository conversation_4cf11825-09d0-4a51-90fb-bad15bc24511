"""Voice WebSocket handler"""
import json
import asyncio
import websockets
from typing import List, Dict, Any
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session

from app.services.voice_service import VoiceService
from app.services.azure_service import AzureWebSocketService
from app.websocket.message_handlers import MessageHandler
from app.config.voice_config import VoiceConfig
from app.exceptions.voice_exceptions import VoiceServiceException
from app.logger import app_logger as logger

class VoiceWebSocketHandler:
    """Handler for voice WebSocket connections"""

    def __init__(self):
        self.voice_service = VoiceService()
        self.azure_service = AzureWebSocketService()
        self.message_handler = MessageHandler()
        self.config = VoiceConfig()

        # Connection state
        self.conversation_stored = False
        self.transcript = []
        self.fallback_count = 0
        self.is_speaking = False
        self.last_active = None

    async def handle_connection(
        self,
        websocket: WebSocket,
        agent_role: str,
        language: str,
        db: Session
    ):
        """Handle WebSocket connection lifecycle"""
        try:
            await websocket.accept()

            # Initialize state
            self.conversation_stored = False
            self.transcript = []
            self.fallback_count = 0

            # Authenticate user
            user = await self.voice_service.authenticate_user(websocket)
            logger.info(f"WebSocket connection with agent_role: {agent_role}")

            # Setup Azure connection
            azure_ws, headers = await self.voice_service.setup_azure_connection(db)
            logger.info("Connected to Azure WebSocket.")

            # Get agent instructions for Azure session (clean instructions for real-time processing)
            context = self.voice_service.create_agent_context(user, agent_role, language)
            agent_instructions = await self.voice_service.get_agent_instructions(db, context, for_audio_text=True)
            
            logger.info(f"agent_instructions: {agent_instructions}")

            # Configure session
            voice_model = self.voice_service.get_voice_model(user)
            await self.voice_service.configure_session(azure_ws, agent_instructions, voice_model, language)

            # Initialize activity tracking
            self.last_active = asyncio.Event()

            # Get MOA context for initial message (full MOA processing with tags + transcripts)
            moa_context = ""
            if agent_role == "ppca":
                # Send loading status to client
                await websocket.send_json({
                    "type": "loading_status",
                    "status": "loading",
                    "message": "Preparing personalized conversation context...",
                    "progress": 10
                })

                try:
                    # Wait for PPCA instructions to be fully received
                    moa_context = await self.voice_service.get_agent_instructions(db, context, for_audio_text=False)

                    # Send completion status to client
                    await websocket.send_json({
                        "type": "loading_status",
                        "status": "complete",
                        "message": "Context ready!",
                        "progress": 100
                    })

                except Exception as e:
                    logger.error(f"Error getting PPCA instructions: {e}")
                    # Send error status to client
                    await websocket.send_json({
                        "type": "loading_status",
                        "status": "error",
                        "message": "Failed to load context, using fallback",
                        "progress": 100
                    })
                    # Use fallback context
                    moa_context = self.voice_service.agent_service._get_fallback_moa_context(language, context.user_first_name)

            # Get initial message - for PPCA use MOA context directly
            if agent_role == "ppca" and moa_context:
                initial_message = moa_context  # Use MOA output directly as initial message
            else:
                initial_message = self.voice_service.get_initial_message(
                    agent_role, language, context, moa_context
                )


            logger.info(f"initial_message: {initial_message}")

            # Start message handling tasks
            await asyncio.gather(
                self._handle_client_messages(websocket, azure_ws, initial_message, user, agent_role, language, db, agent_instructions),
                self._handle_azure_messages(websocket, azure_ws),
                # self._handle_inactivity(websocket, azure_ws, language)
            )

        except Exception as e:
            logger.error(f"WebSocket error: {str(e)}", exc_info=True)
            raise
        finally:
            await self._cleanup_connection(user, agent_role, language, db, agent_instructions)

    async def _handle_client_messages(
        self,
        websocket: WebSocket,
        azure_ws,
        initial_message: str,
        user,
        agent_role: str,
        language: str,
        db: Session,
        agent_instructions: str
    ):
        """Handle messages from client"""
        try:
            # Send initial message if provided
            if initial_message:
                await self._send_initial_message(azure_ws, initial_message)

            while True:
                message = await websocket.receive_json()
                self._reset_activity()

                if message["type"] == "audio":
                    await self._handle_audio_message(azure_ws, message)
                elif message["type"] == "text":
                    await self._handle_text_message(azure_ws, message)

        except WebSocketDisconnect:
            logger.info(f"Client disconnected: {user.email}")
            await self._store_conversation_on_disconnect(
                user, agent_role, language, db, agent_instructions
            )
        except Exception as e:
            logger.error(f"Error in client message handler: {e}", exc_info=True)
            await self._store_conversation_on_error(
                user, agent_role, language, db, agent_instructions
            )

    async def _handle_azure_messages(self, websocket: WebSocket, azure_ws):
        """Handle messages from Azure"""
        try:
            while True:
                message = await azure_ws.recv()
                data = json.loads(message)

                # Forward to client
                try:
                    await websocket.send_json(data)
                except Exception as e:
                    logger.error(f"Error sending to client: {e}", exc_info=True)
                    break

                self._reset_activity()

                # Process message for transcript
                await self._process_azure_message(data)

        except websockets.exceptions.ConnectionClosed:
            logger.info("Azure connection closed")
        except Exception as e:
            logger.error(f"Error in Azure message handler: {e}", exc_info=True)
        finally:
            # Add placeholder messages if needed
            self.transcript = self.voice_service.conversation_service.add_placeholder_messages(
                self.transcript
            )

    async def _handle_inactivity(self, websocket: WebSocket, azure_ws, language: str):
        """Handle connection inactivity"""
        try:
            while True:
                try:
                    # Wait for activity
                    await asyncio.wait_for(self.last_active.wait(), timeout=self.config.INACTIVITY_TIMEOUT)
                    self.last_active.clear()

                except asyncio.TimeoutError:
                    if self.fallback_count >= self.config.MAX_FALLBACKS:
                        logger.warning(f"Max fallbacks reached ({self.config.MAX_FALLBACKS}). Closing connection.")
                        await websocket.close(code=1000, reason="Inactivity limit exceeded")
                        return

                    self.fallback_count += 1
                    logger.info(f"Sending fallback prompt ({self.fallback_count}/{self.config.MAX_FALLBACKS})")

                    try:
                        self.is_speaking = False
                        await self.azure_service.send_fallback_prompt(azure_ws, language)

                        # Refresh activity tracker
                        self.last_active.set()
                        self.last_active.clear()

                    except websockets.exceptions.ConnectionClosed:
                        logger.error("Azure WS closed during fallback.")
                        break

        except Exception as e:
            logger.error(f"Inactivity handler crashed: {e}", exc_info=True)

    async def _send_initial_message(self, azure_ws, initial_message: str):
        """Send initial message to start conversation"""
        logger.info(f"Sending initial message: {initial_message[:50]}...")

        self.transcript.append({
            "role": "assistant",
            "content": initial_message,
            "timestamp": datetime.utcnow().isoformat()
        })

        await self.azure_service.send_text_message(azure_ws, initial_message)

    async def _handle_audio_message(self, azure_ws, message: Dict[str, Any]):
        """Handle audio message from client"""
        try:
            base64_audio = self.message_handler.process_audio_message(message)
            if base64_audio:
                await self.azure_service.send_audio_data(azure_ws, base64_audio)
        except Exception as e:
            logger.error(f"Error processing audio: {e}", exc_info=True)

    async def _handle_text_message(self, azure_ws, message: Dict[str, Any]):
        """Handle text message from client"""
        # Store user message in transcript
        self.transcript.append({
            "role": "user",
            "content": message["data"],
            "timestamp": datetime.utcnow().isoformat()
        })

        await self.azure_service.send_text_message(azure_ws, message["data"])

    async def _process_azure_message(self, data: Dict[str, Any]):
        """Process message from Azure for transcript"""
        message_type = data.get("type", "")

        if message_type == "response.text.delta":
            self.transcript = self.message_handler.process_text_delta(data, self.transcript)
        elif message_type in ["response.completed", "response.done"]:
            self.transcript = self.message_handler.process_response_completed(self.transcript)
        elif message_type == "message":
            self.transcript = self.message_handler.process_direct_message(data, self.transcript)
        elif message_type == "conversation.item.input_audio_transcription.completed":
            self.transcript = self.message_handler.process_audio_transcription(data, self.transcript)
        elif message_type == "conversation.history":
            self.transcript = self.message_handler.process_conversation_history(data, self.transcript)
        elif message_type == "conversation.item.created":
            self.transcript = self.message_handler.process_conversation_item_created(data, self.transcript)
        elif message_type == "response.audio_transcript.delta":
            self.transcript = self.message_handler.process_audio_transcript_delta(data, self.transcript)
            self.is_speaking = True

    def _reset_activity(self):
        """Reset activity tracking"""
        if self.last_active:
            self.last_active.set()
            self.last_active.clear()
        self.fallback_count = 0

    async def _store_conversation_on_disconnect(
        self, user, agent_role: str, language: str, db: Session, agent_instructions: str
    ):
        """Store conversation when client disconnects"""
        try:
            if not self.conversation_stored and self.transcript:
                metadata = self._create_metadata(agent_role, agent_instructions)
                self.voice_service.store_conversation(
                    db, user.id, agent_role, self.transcript, language, metadata
                )
                self.conversation_stored = True

                # Store PPCA instructions if needed
                if self.voice_service.should_store_ppca_instructions(agent_role):
                    self.voice_service.create_ppca_task(db, user.id, language, self.transcript)

        except Exception as e:
            logger.error(f"Error during disconnect cleanup: {e}", exc_info=True)

    async def _store_conversation_on_error(
        self, user, agent_role: str, language: str, db: Session, agent_instructions: str
    ):
        """Store conversation when error occurs"""
        try:
            if not self.conversation_stored and self.transcript:
                metadata = self._create_metadata(agent_role, agent_instructions)
                self.voice_service.store_conversation(
                    db, user.id, agent_role, self.transcript, language, metadata
                )
                self.conversation_stored = True

        except Exception as cleanup_error:
            logger.error(f"Error during error cleanup: {cleanup_error}", exc_info=True)

    async def _cleanup_connection(
        self, user, agent_role: str, language: str, db: Session, agent_instructions: str
    ):
        """Final cleanup when connection ends"""
        if not self.conversation_stored and self.transcript and len(self.transcript) > 0:
            try:
                # Add placeholder if no assistant messages
                has_assistant_message = any(msg["role"] == "assistant" for msg in self.transcript)

                if not has_assistant_message and len(self.transcript) > 0:
                    logger.warning("No assistant messages found in transcript, adding placeholder")
                    self.transcript.append({
                        "role": "assistant",
                        "content": "Session ended without complete response",
                        "timestamp": datetime.utcnow().isoformat(),
                        "is_placeholder": True
                    })

                # Store the conversation
                metadata = self._create_metadata(agent_role, agent_instructions)
                self.voice_service.store_conversation(
                    db, user.id, agent_role, self.transcript, language, metadata
                )
                logger.info("Conversation stored in finally block")

            except Exception as e:
                logger.error(f"Error storing conversation in finally block: {e}", exc_info=True)

    def _create_metadata(self, agent_role: str, agent_instructions: str) -> Dict[str, Any]:
        """Create metadata for conversation storage"""
        if agent_role == "ppca":
            return {"instructions": agent_instructions}
        else:
            return {"source": "voice_server"}
