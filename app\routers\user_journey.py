from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app import schemas, models, crud
from app.auth import get_db, get_current_user
from datetime import datetime
from typing import Optional
from sqlalchemy import desc

router = APIRouter(prefix="/journey", tags=["user_journey"])

@router.get("/current-agent")
def get_current_agent(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    if not current_user.first_time_onboarded:
        if current_user.current_stage == models.UserStageEnum.NEW:
            agent = crud.get_agent_by_type(db, models.AgentTypeEnum.OBA)
            return {"agent": agent, "context": None}
        elif current_user.current_stage == models.UserStageEnum.CONFIGURATION:
            agent = crud.get_agent_by_type(db, models.AgentTypeEnum.PPCA_CONFIG)
            return {"agent": agent, "context": current_user.onboarding_data}
    
    # Regular PPCA interaction
    agent = crud.get_agent_by_type(db, models.AgentTypeEnum.PPCA)
    return {
        "agent": agent,
        "context": {
            "config": current_user.ppca_config_data,
            "history": crud.get_recent_conversations(db, current_user.id, limit=5)
        }
    }

@router.post("/set-stage/{stage}")
def set_user_stage(
    stage: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    # Validate the requested stage
    try:
        new_stage = models.UserStageEnum(stage)
    except ValueError:
        raise HTTPException(
            status_code=400, 
            detail=f"Invalid stage value. Must be one of {[s.value for s in models.UserStageEnum]}"
        )
    
    # Set the new stage
    current_user.current_stage = new_stage.value
    
    # Update timestamp
    current_user.updated_at = datetime.utcnow()
    
    # If moving to REGULAR, mark as onboarded
    if new_stage == models.UserStageEnum.REGULAR:
        current_user.first_time_onboarded = True
    
    db.commit()
    return {"status": "success", "current_stage": current_user.current_stage}

@router.post("/conversation")
def store_conversation(
    conversation: schemas.ConversationCreate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    # Try to find existing conversation
    existing_conversation = db.query(models.Conversation).filter(
        models.Conversation.user_id == current_user.id,
        models.Conversation.agent_role == conversation.agent_role
    ).order_by(desc(models.Conversation.started_at)).first()

    session_data = {
        "messages": conversation.transcript,
        "started_at": conversation.started_at,
        "ended_at": datetime.utcnow(),
        "metadata": conversation.metadata or {}
    }

    if existing_conversation:
        # Update existing conversation
        conversation_data = existing_conversation.conversation_data
        if "sessions" not in conversation_data:
            # Convert old format to new format
            old_messages = conversation_data.get("messages", [])
            old_metadata = conversation_data.get("metadata", {})
            conversation_data["sessions"] = [{
                "messages": old_messages,
                "started_at": existing_conversation.started_at,
                "ended_at": existing_conversation.ended_at,
                "metadata": old_metadata
            }]
            del conversation_data["messages"]
            del conversation_data["metadata"]
        
        # Append new session
        conversation_data["sessions"].append(session_data)
        existing_conversation.conversation_data = conversation_data
        existing_conversation.ended_at = datetime.utcnow()
        db.commit()
        db.refresh(existing_conversation)
        return existing_conversation
    else:
        # Create new conversation
        new_conversation = models.Conversation(
            user_id=current_user.id,
            agent_id=conversation.agent_id,
            agent_role=conversation.agent_role,
            conversation_data={
                "agent": crud.get_agent_by_role(db, conversation.agent_role),
                "sessions": [session_data]
            },
            started_at=conversation.started_at,
            ended_at=datetime.utcnow()
        )
        db.add(new_conversation)
        db.commit()
        db.refresh(new_conversation)
        return new_conversation
