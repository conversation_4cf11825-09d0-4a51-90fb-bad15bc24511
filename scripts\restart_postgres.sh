#!/bin/bash

# Usage of the script with optional volume argument
# ./restart_postgres.sh [postgres_volume]

POSTGRES_VOLUME=${1:-""}  # Default is empty if not provided

# Stop and remove the existing container
echo "Stopping and removing existing PostgreSQL container..."
docker stop lumalife_postgres
docker rm lumalife_postgres

# Start the PostgreSQL container with optional volume
echo "Starting PostgreSQL container..."
if [[ -n "$POSTGRES_VOLUME" ]]; then
    docker run -p 5432:5432 \
        --name lumalife_postgres \
        -e POSTGRES_PASSWORD=password \
        -e POSTGRES_DB=lumalife \
        -d \
        -v $POSTGRES_VOLUME:/var/lib/postgresql/data \
        postgres:latest \
        -c max_connections=250
else
    docker run -p 5432:5432 \
        --name lumalife_postgres \
        -e POSTGRES_PASSWORD=password \
        -e POSTGRES_DB=lumalife \
        -d \
        postgres:latest \
        -c max_connections=250
fi

# Ensure alembic runs in the correct directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PARENT_DIR="$(dirname "$SCRIPT_DIR")"
cd "$PARENT_DIR"

# Give Postgres a moment to start
echo "Waiting for PostgreSQL to start..."
sleep 3

# Run Alembic migration
echo "Running Alembic migration..."
alembic upgrade head

echo "PostgreSQL container restarted and migration completed."