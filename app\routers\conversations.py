from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from typing import Optional
from app import crud, schemas, models
from app.auth import get_current_user
from app.database import get_db

router = APIRouter(prefix="/conversations", tags=["conversations"])

@router.get("/", response_model=list[schemas.ConversationResponse])
def get_user_conversations(
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    limit: Optional[int] = Query(default=30, le=100),
    agent_role: Optional[str] = None,  # Changed from agent_type
    user_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get conversations for the current user or specified user (admin only)
    """
    if user_id and current_user.role != models.RoleEnum.admin:
        raise HTTPException(
            status_code=403,
            detail="Only admins can access other users' conversations"
        )
    
    target_user_id = user_id or current_user.id
    
    conversations = crud.get_user_conversations(
        db=db,
        user_id=target_user_id,
        start_date=start_date,
        end_date=end_date,
        limit=limit,
        agent_role=agent_role  # Changed from agent_type
    )
    return conversations

@router.get("/by-id/{conversation_id}", response_model=schemas.ConversationResponse)
def get_conversation(
    conversation_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """Get a specific conversation by ID"""
    conversation = db.query(models.Conversation).filter(
        models.Conversation.id == conversation_id
    ).first()
    
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")
        
    # Check if user has access to this conversation
    if conversation.user_id != current_user.id and current_user.role != models.RoleEnum.admin:
        raise HTTPException(status_code=403, detail="Not authorized to access this conversation")
        
    return conversation

@router.get("/by-user/{user_id}", response_model=list[schemas.ConversationResponse])
def get_conversations_by_user_id(
    user_id: int,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    agent_role: Optional[str] = None,
    limit: Optional[int] = Query(default=30, le=100),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get all conversations for a specific user ID.
    Users can access their own conversations.
    Admins can access any user's conversations.
    """
    if current_user.id != user_id and current_user.role != models.RoleEnum.admin:
        raise HTTPException(
            status_code=403,
            detail="Not authorized to access these conversations"
        )
    
    target_user = db.query(models.User).filter(models.User.id == user_id).first()
    if not target_user:
        raise HTTPException(status_code=404, detail="User not found")
    
    conversations = crud.get_user_conversations(
        db=db,
        user_id=user_id,
        start_date=start_date,
        end_date=end_date,
        limit=limit,
        agent_role=agent_role
    )
    return conversations
