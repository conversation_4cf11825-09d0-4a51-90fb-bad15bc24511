from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_
from typing import List, Optional, Any
from app import models, schemas, crud
from app.auth import get_current_user, get_db
from pydantic import BaseModel
from datetime import datetime

router = APIRouter(prefix="/admin", tags=["admin"])

async def get_current_admin(current_user: models.User = Depends(get_current_user)):
    if current_user.role != models.RoleEnum.admin:
        raise HTTPException(status_code=403, detail="Admin access required")
    return current_user

@router.get("/users", response_model=List[schemas.UserList])
async def list_users(
    is_approved: Optional[bool] = None,
    role: Optional[str] = None,
    gender: Optional[str] = None,
    current_stage: Optional[str] = None,
    email: Optional[str] = None,
    created_after: Optional[datetime] = None,
    created_before: Optional[datetime] = None,
    sort_by: Optional[str] = Query("created_at", description="Field to sort by"),
    sort_desc: bool = Query(True, description="Sort in descending order"),
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    db: Session = Depends(get_db),
    admin: models.User = Depends(get_current_admin)
):
    query = db.query(models.User)
    
    # Apply filters
    if is_approved is not None:
        query = query.filter(models.User.is_approved == is_approved)
    if role:
        query = query.filter(models.User.role == role)
    if gender:
        query = query.filter(models.User.gender == gender)
    if current_stage:
        query = query.filter(models.User.current_stage == current_stage)
    if email:
        query = query.filter(models.User.email.ilike(f"%{email}%"))
    
    # Apply date filters
    if created_after:
        query = query.filter(models.User.created_at >= created_after)
    if created_before:
        query = query.filter(models.User.created_at <= created_before)
    
    # Apply sorting
    if hasattr(models.User, sort_by):
        order_field = getattr(models.User, sort_by)
        if sort_desc:
            query = query.order_by(order_field.desc())
        else:
            query = query.order_by(order_field)
    else:
        # Default sort by created_at if invalid field provided
        query = query.order_by(models.User.created_at.desc() if sort_desc else models.User.created_at)
    
    # Pagination
    skip = (page - 1) * limit
    users = query.offset(skip).limit(limit).all()
    
    return users

@router.get("/users/pending-approval", response_model=List[schemas.UserList])
async def list_pending_users(
    db: Session = Depends(get_db),
    admin: models.User = Depends(get_current_admin),
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100)
):
    skip = (page - 1) * limit
    query = db.query(models.User).filter(models.User.is_approved == False)
    users = query.offset(skip).limit(limit).all()
    return users

@router.get("/users/stats")
async def get_user_stats(
    db: Session = Depends(get_db),
    admin: models.User = Depends(get_current_admin)
):
    total_users = db.query(models.User).count()
    pending_approval = db.query(models.User).filter(models.User.is_approved == False).count()
    approved_users = db.query(models.User).filter(models.User.is_approved == True).count()
    
    return {
        "total_users": total_users,
        "pending_approval": pending_approval,
        "approved_users": approved_users,
        "by_gender": {
            "male": db.query(models.User).filter(models.User.gender == "male").count(),
            "female": db.query(models.User).filter(models.User.gender == "female").count(),
            "other": db.query(models.User).filter(models.User.gender == "other").count()
        }
    }

# First, define all the bulk operations
@router.put("/users/bulk-approve", response_model=List[schemas.UserList])
async def bulk_approve_users(
    user_ids: List[int],
    approve: bool = True,
    db: Session = Depends(get_db),
    admin: models.User = Depends(get_current_admin)
):
    """
    Bulk approve or reject users. Set approve=true to approve, approve=false to reject.
    Admin users are skipped and their approval status is not changed.
    """
    # Get all users with the given IDs
    users = db.query(models.User).filter(models.User.id.in_(user_ids)).all()
    
    # Filter out admin users to prevent changing their status
    non_admin_users = [user for user in users if user.role != models.RoleEnum.admin]
    
    # Update approval status only for non-admin users
    for user in non_admin_users:
        user.is_approved = approve
        user.updated_at = datetime.utcnow()
    
    db.commit()
    
    # Return the updated users
    return non_admin_users

class UserUpdateItem(BaseModel):
    user_id: int
    max_token_limit: Optional[int] = None
    remaining_time: Optional[int] = None

class BulkIndividualUserUpdate(BaseModel):
    updates: List[UserUpdateItem]

@router.put("/users/bulk-update-individual", response_model=List[schemas.UserList])
async def bulk_update_individual_users(
    update_data: BulkIndividualUserUpdate,
    db: Session = Depends(get_db),
    admin: models.User = Depends(get_current_admin)
):
    """
    Bulk update users with individual values for each user.
    Example:
    {
        "updates": [
            {"user_id": 1, "max_token_limit": 100, "remaining_time": 120},
            {"user_id": 2, "max_token_limit": 200, "remaining_time": 240}
        ]
    }
    """
    updated_users = []
    
    # Process each user update individually
    for update_item in update_data.updates:
        user = db.query(models.User).filter(models.User.id == update_item.user_id).first()
        if user:
            if update_item.max_token_limit is not None:
                user.max_token_limit = update_item.max_token_limit
                # Recalculate tokens_remaining
                user.remaining_time = user.max_token_limit - user.tokens_used
                
            if update_item.remaining_time is not None:
                user.remaining_time = update_item.remaining_time
            
            user.updated_at = datetime.utcnow()
            updated_users.append(user)
    
    db.commit()
    
    # Refresh all users to get updated data
    user_ids = [update.user_id for update in update_data.updates]
    refreshed_users = db.query(models.User).filter(models.User.id.in_(user_ids)).all()
    
    return refreshed_users

# Then define the individual user operations
@router.put("/users/{user_id}/approve", response_model=schemas.UserList)
async def approve_user(
    user_id: int,
    approve: bool = True,
    db: Session = Depends(get_db),
    admin: models.User = Depends(get_current_admin)
):
    """
    Approve or reject a user. Set approve=true to approve, approve=false to reject.
    Cannot change approval status of admin users.
    """
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Prevent changing approval status of admin users
    if user.role == models.RoleEnum.admin:
        raise HTTPException(
            status_code=403, 
            detail="Cannot change approval status of admin users"
        )
    
    user.is_approved = approve
    user.updated_at = datetime.utcnow()
    
    db.commit()
    db.refresh(user)
    return user

@router.put("/users/{user_id}/token-limit", response_model=schemas.UserList)
async def update_user_token_limit(
    user_id: int,
    token_update: schemas.TokenLimitUpdate,
    db: Session = Depends(get_db),
    admin: models.User = Depends(get_current_admin)
):
    """
    Update a user's maximum token limit
    """
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    user.max_token_limit = token_update.max_token_limit
    # Recalculate tokens_remaining
    user.tokens_remaining = user.max_token_limit - user.tokens_used
    user.updated_at = datetime.utcnow()
    
    db.commit()
    db.refresh(user)
    return user

class RemainingTimeUpdate(BaseModel):
    remaining_time: int

@router.put("/users/{user_id}/remaining-time", response_model=schemas.UserList)
async def update_user_remaining_time(
    user_id: int,
    time_update: RemainingTimeUpdate,
    db: Session = Depends(get_db),
    admin: models.User = Depends(get_current_admin)
):
    """
    Update a user's remaining time (in seconds)
    """
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    user.remaining_time = time_update.remaining_time
    user.updated_at = datetime.utcnow()
    
    db.commit()
    db.refresh(user)
    return user

class AdminUserUpdate(BaseModel):
    email: Optional[str] = None
    remaining_time: Optional[int] = None
    max_token_limit: Optional[int] = None
    is_approved: Optional[bool] = None
    role: Optional[str] = None

@router.put("/users/{user_id}", response_model=schemas.UserList)
async def admin_update_user(
    user_id: int,
    user_update: AdminUserUpdate,
    db: Session = Depends(get_db),
    admin: models.User = Depends(get_current_admin)
):
    """
    Admin endpoint to update user fields including restricted ones like
    remaining_time, max_token_limit, etc.
    """
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Update fields if provided
    update_data = user_update.dict(exclude_unset=True)
    for key, value in update_data.items():
        if value is not None:
            setattr(user, key, value)
    
    # Update the updated_at timestamp
    user.updated_at = datetime.utcnow()
    
    db.commit()
    db.refresh(user)
    return user

class ConfigurationCreate(BaseModel):
    key: str
    value: Any

class ConfigurationResponse(BaseModel):
    id: int
    key: str
    value: Any
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

@router.get("/configurations", response_model=List[ConfigurationResponse])
async def list_configurations(
    db: Session = Depends(get_db),
    admin: models.User = Depends(get_current_admin)
):
    """List all configuration entries in the system"""
    return db.query(models.Configuration).all()

@router.get("/configurations/{key}", response_model=ConfigurationResponse)
async def get_configuration(
    key: str,
    db: Session = Depends(get_db),
    admin: models.User = Depends(get_current_admin)
):
    """Get a specific configuration by key"""
    config = crud.get_configuration_by_key(db, key)
    if not config:
        raise HTTPException(status_code=404, detail="Configuration not found")
    return config

@router.post("/configurations", response_model=ConfigurationResponse)
async def create_configuration(
    config: ConfigurationCreate,
    db: Session = Depends(get_db),
    admin: models.User = Depends(get_current_admin)
):
    """Create a new configuration entry"""
    # Check if configuration with this key already exists
    existing = crud.get_configuration_by_key(db, config.key)
    if existing:
        raise HTTPException(
            status_code=400, 
            detail=f"Configuration with key '{config.key}' already exists"
        )
    
    # Create new configuration
    new_config = models.Configuration(
        key=config.key,
        value=config.value,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    
    db.add(new_config)
    db.commit()
    db.refresh(new_config)
    return new_config

@router.put("/configurations/{key}", response_model=ConfigurationResponse)
async def update_configuration(
    key: str,
    config: ConfigurationCreate,
    db: Session = Depends(get_db),
    admin: models.User = Depends(get_current_admin)
):
    """Update an existing configuration entry"""
    existing = crud.get_configuration_by_key(db, key)
    if not existing:
        raise HTTPException(status_code=404, detail="Configuration not found")
    
    # Update configuration
    existing.value = config.value
    existing.updated_at = datetime.utcnow()
    
    db.commit()
    db.refresh(existing)
    return existing

@router.delete("/configurations/{key}", response_model=dict)
async def delete_configuration(
    key: str,
    db: Session = Depends(get_db),
    admin: models.User = Depends(get_current_admin)
):
    """Delete a configuration entry"""
    existing = crud.get_configuration_by_key(db, key)
    if not existing:
        raise HTTPException(status_code=404, detail="Configuration not found")
    
    db.delete(existing)
    db.commit()
    
    return {"detail": f"Configuration '{key}' deleted successfully"}
