#!/bin/bash

# Stop and remove PostgreSQL container
echo "Stopping and removing PostgreSQL container..."
docker stop lumalife_postgres
docker rm lumalife_postgres

# Remove all alembic-related directories and files
echo "Removing alembic directories and files..."
# rm -rf alembic/
# rm -f alembic.ini



# Clean Python cache files
echo "Cleaning Python cache files..."
find . -type d -name "__pycache__" -exec rm -rf {} +
find . -type f -name "*.pyc" -delete

# Drop and recreate the database with cleanup of custom types
echo "Resetting database..."
docker run -it --rm --network host postgres:latest psql -h localhost -U postgres -c "DROP DATABASE IF EXISTS lumalife;"
docker run -it --rm --network host postgres:latest psql -h localhost -U postgres -c "DROP TYPE IF EXISTS role_enum CASCADE;"
docker run -it --rm --network host postgres:latest psql -h localhost -U postgres -c "DROP TYPE IF EXISTS gender_enum CASCADE;"
docker run -it --rm --network host postgres:latest psql -h localhost -U postgres -c "CREATE DATABASE lumalife;"

echo "Cleanup complete!"
