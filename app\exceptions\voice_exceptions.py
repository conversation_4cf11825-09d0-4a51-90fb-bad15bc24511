"""Voice service exceptions"""

class VoiceServiceException(Exception):
    """Base exception for voice service errors"""
    pass

class AzureConnectionException(VoiceServiceException):
    """Exception for Azure WebSocket connection errors"""
    pass

class ConversationStorageException(VoiceServiceException):
    """Exception for conversation storage errors"""
    pass

class AgentInstructionException(VoiceServiceException):
    """Exception for agent instruction processing errors"""
    pass

class AudioProcessingException(VoiceServiceException):
    """Exception for audio processing errors"""
    pass
