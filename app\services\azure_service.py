"""Azure WebSocket service for voice interactions"""
import json
import websockets
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from app import crud
from app.config.voice_config import VoiceConfig
from app.exceptions.voice_exceptions import AzureConnectionException
from app.logger import app_logger as logger

class AzureWebSocketService:
    """Service for managing Azure WebSocket connections"""

    def __init__(self):
        self.config = VoiceConfig()

    async def get_azure_config(self, db: Session) -> tuple[Optional[str], Optional[str]]:
        """Get Azure configuration from database"""
        try:
            azure_key = crud.get_configuration_by_key(db, "AZURE_OPENAI_API_KEY")
            azure_url = crud.get_configuration_by_key(db, "AZURE_URL")
            return (
                azure_key.value if azure_key else None,
                azure_url.value if azure_url else None
            )
        except Exception as e:
            logger.error(f"Error getting Azure config: {e}", exc_info=True)
            raise AzureConnectionException(f"Failed to get Azure configuration: {e}")

    def create_azure_headers(self, api_key: str) -> Dict[str, str]:
        """Create headers for Azure WebSocket connection"""
        return {
            "api-key": api_key,
            "OpenAI-Beta": "realtime=v1"
        }

    def create_session_config(self, instructions: str, voice_model: str, language: str = "en") -> Dict[str, Any]:
        """Create session configuration for Azure WebSocket"""
        return {
            "type": "session.update",
            "session": {
                "modalities": ["audio", "text"],
                "instructions": instructions,
                "voice": voice_model,
                "input_audio_format": self.config.AUDIO_FORMAT,
                "output_audio_format": self.config.AUDIO_FORMAT,
                "input_audio_transcription": {"model": "whisper-1", "language": language},
                "turn_detection": self.config.TURN_DETECTION,
            }
        }

    async def connect_to_azure(self, url: str, headers: Dict[str, str]):
        """Connect to Azure WebSocket"""
        try:
            return await websockets.connect(url, additional_headers=headers)
        except Exception as e:
            logger.error(f"Failed to connect to Azure WebSocket: {e}", exc_info=True)
            raise AzureConnectionException(f"Azure WebSocket connection failed: {e}")

    async def send_session_config(self, websocket, session_config: Dict[str, Any]):
        """Send session configuration to Azure WebSocket"""
        try:
            await websocket.send(json.dumps(session_config))
            await websocket.recv()  # Wait for acknowledgment
            logger.info("Session configuration sent successfully")
        except Exception as e:
            logger.error(f"Failed to send session config: {e}", exc_info=True)
            raise AzureConnectionException(f"Failed to configure session: {e}")

    def get_voice_model(self, user_gender: Optional[str]) -> str:
        """Get appropriate voice model based on user gender"""
        if not user_gender:
            return self.config.VOICE_MODELS["default"]

        gender_key = user_gender.lower()
        return self.config.VOICE_MODELS.get(gender_key, self.config.VOICE_MODELS["default"])

    async def send_message(self, websocket, message_type: str, content: Any):
        """Send a message to Azure WebSocket"""
        try:
            message = {
                "type": message_type,
                **content
            }
            await websocket.send(json.dumps(message))
        except Exception as e:
            logger.error(f"Failed to send message to Azure: {e}", exc_info=True)
            raise AzureConnectionException(f"Failed to send message: {e}")

    async def send_text_message(self, websocket, text: str):
        """Send a text message to Azure WebSocket"""
        await self.send_message(websocket, "conversation.item.create", {
            "item": {
                "type": "message",
                "role": "user",
                "content": [{
                    "type": "input_text",
                    "text": text
                }]
            }
        })
        await self.send_message(websocket, "response.create", {})

    async def send_audio_data(self, websocket, audio_data: str):
        """Send audio data to Azure WebSocket"""
        await self.send_message(websocket, "input_audio_buffer.append", {
            "audio": audio_data
        })

    async def send_fallback_prompt(self, websocket, language: str = "en"):
        """Send fallback prompt when user is inactive"""
        fallback_text = (
            "Are you still there? Let me know how I can help."
            if language == "en"
            else "Sind Sie noch da? Lassen Sie mich wissen, wie ich helfen kann."
        )
        await self.send_text_message(websocket, fallback_text)
