"""WebSocket message handlers"""
import json
import base64
import numpy as np
from typing import List, Dict, Any
from datetime import datetime

from app.exceptions.voice_exceptions import AudioProcessingException
from app.logger import app_logger as logger

class MessageHandler:
    """Handler for processing WebSocket messages"""
    
    def __init__(self):
        self.current_assistant_message = ""
        self.assistant_message_index = -1
    
    def process_audio_message(self, message: Dict[str, Any]) -> str:
        """Process audio message and return base64 encoded audio"""
        try:
            audio_bytes = base64.b64decode(message["audio"])
            if not audio_bytes:
                return ""
            
            # Ensure even number of bytes for int16
            if len(audio_bytes) % 2 != 0:
                audio_bytes = audio_bytes[:-1]
            
            # Convert to numpy array
            audio_data = np.frombuffer(audio_bytes, dtype=np.int16)
            
            # Handle multi-channel audio by taking mean
            if len(audio_data.shape) > 1:
                audio_data = audio_data.mean(axis=1).astype(np.int16)
            
            if len(audio_data) == 0:
                return ""
            
            # Convert back to bytes and encode
            processed_audio = audio_data.tobytes()
            return base64.b64encode(processed_audio).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error processing audio: {e}", exc_info=True)
            raise AudioProcessingException(f"Audio processing failed: {e}")
    
    def process_text_delta(
        self, 
        data: Dict[str, Any], 
        transcript: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Process text delta from Azure response"""
        if "delta" not in data:
            return transcript
        
        delta = data.get("delta", "")
        self.current_assistant_message += delta
        
        # If this is the first delta for this message, create a new transcript entry
        if self.assistant_message_index == -1:
            transcript.append({
                "role": "assistant",
                "content": self.current_assistant_message,
                "timestamp": datetime.utcnow().isoformat()
            })
            self.assistant_message_index = len(transcript) - 1
        else:
            # Update the existing assistant message
            if 0 <= self.assistant_message_index < len(transcript):
                transcript[self.assistant_message_index]["content"] = self.current_assistant_message
            else:
                logger.warning(f"Invalid assistant message index: {self.assistant_message_index}")
                # Create a new message if index is invalid
                transcript.append({
                    "role": "assistant",
                    "content": self.current_assistant_message,
                    "timestamp": datetime.utcnow().isoformat()
                })
                self.assistant_message_index = len(transcript) - 1
        
        return transcript
    
    def process_response_completed(
        self, 
        transcript: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Process response completion"""
        if self.current_assistant_message:
            # Make sure the assistant message is in the transcript
            if self.assistant_message_index == -1:
                transcript.append({
                    "role": "assistant",
                    "content": self.current_assistant_message,
                    "timestamp": datetime.utcnow().isoformat()
                })
                logger.info("Added final assistant message to transcript")
            
            # Reset for next message
            self.current_assistant_message = ""
            self.assistant_message_index = -1
        
        return transcript
    
    def process_direct_message(
        self, 
        data: Dict[str, Any], 
        transcript: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Process direct assistant message"""
        if data.get("role") != "assistant" or "content" not in data:
            return transcript
        
        content = data.get("content", "")
        logger.info(f"Direct assistant message: '{content[:50]}...'")
        
        # Check if this message is already in the transcript
        message_exists = any(
            msg["role"] == "assistant" and msg["content"] == content 
            for msg in transcript
        )
        
        if not message_exists:
            transcript.append({
                "role": "assistant",
                "content": content,
                "timestamp": datetime.utcnow().isoformat()
            })
            logger.info("Added direct assistant message to transcript")
        
        return transcript
    
    def process_audio_transcription(
        self, 
        data: Dict[str, Any], 
        transcript: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Process user audio transcription"""
        transcript_text = data.get("transcript", "")
        logger.info(f"User audio transcription: '{transcript_text[:50]}...'")
        
        # Check if this transcription is already in the transcript
        transcription_exists = any(
            msg["role"] == "user" and msg["content"] == transcript_text 
            for msg in transcript
        )
        
        if not transcription_exists:
            transcript.append({
                "role": "user",
                "content": transcript_text,
                "timestamp": datetime.utcnow().isoformat()
            })
            logger.info("Added transcribed user message to transcript")
            
            # Reset assistant message tracking for the next response
            self.current_assistant_message = ""
            self.assistant_message_index = -1
        
        return transcript
    
    def process_conversation_history(
        self, 
        data: Dict[str, Any], 
        transcript: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Process conversation history from Azure"""
        history = data.get("history", [])
        logger.info(f"Received conversation history with {len(history)} items")
        
        # Process each history item
        for item in history:
            if item.get("role") == "assistant" and "content" in item:
                content = item.get("content", "")
                logger.info(f"History assistant message: '{content[:50]}...'")
                
                # Check if this message is already in the transcript
                message_exists = any(
                    msg["role"] == "assistant" and msg["content"] == content 
                    for msg in transcript
                )
                
                if not message_exists:
                    transcript.append({
                        "role": "assistant",
                        "content": content,
                        "timestamp": datetime.utcnow().isoformat()
                    })
                    logger.info("Added assistant message from history")
        
        return transcript
    
    def process_conversation_item_created(
        self, 
        data: Dict[str, Any], 
        transcript: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Process conversation item creation"""
        item = data.get("item", {})
        if item.get("role") != "assistant" or "content" not in item:
            return transcript
        
        content_list = item.get("content", [])
        for content_item in content_list:
            if content_item.get("type") == "text" and "text" in content_item:
                text = content_item.get("text", "")
                logger.info(f"Assistant conversation item: '{text[:50]}...'")
                
                # Check if this message is already in the transcript
                message_exists = any(
                    msg["role"] == "assistant" and msg["content"] == text 
                    for msg in transcript
                )
                
                if not message_exists:
                    transcript.append({
                        "role": "assistant",
                        "content": text,
                        "timestamp": datetime.utcnow().isoformat()
                    })
                    logger.info("Added assistant message from conversation item")
        
        return transcript
    
    def process_audio_transcript_delta(
        self, 
        data: Dict[str, Any], 
        transcript: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Process audio transcript delta"""
        delta = data.get("delta", "")
        logger.info(f"Audio transcript delta: '{delta}'")
        
        # This is the transcript of what the assistant is saying
        if delta and not self.current_assistant_message:
            self.current_assistant_message = delta
            transcript.append({
                "role": "assistant",
                "content": self.current_assistant_message,
                "timestamp": datetime.utcnow().isoformat(),
                "source": "audio_transcript"
            })
            self.assistant_message_index = len(transcript) - 1
            logger.info(f"Created new assistant message from audio transcript at index {self.assistant_message_index}")
        elif delta and self.assistant_message_index >= 0:
            # Update existing message
            if 0 <= self.assistant_message_index < len(transcript):
                if transcript[self.assistant_message_index].get("source") == "audio_transcript":
                    self.current_assistant_message += delta
                    transcript[self.assistant_message_index]["content"] = self.current_assistant_message
                    logger.debug(f"Updated assistant audio transcript at index {self.assistant_message_index}")
        
        return transcript
    
    def reset_message_state(self):
        """Reset message handler state"""
        self.current_assistant_message = ""
        self.assistant_message_index = -1
