from sqlalchemy.orm import Session
from . import models, schemas, auth
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from sqlalchemy import desc

def get_user_by_email(db: Session, email: str):
    return db.query(models.User).filter(models.User.email == email).first()

def create_user(db: Session, user: schemas.UserCreate):
    hashed_pw = auth.get_password_hash(user.password)
    db_user = models.User(
        email=user.email,
        hashed_password=hashed_pw,
        is_approved=False,
        role=models.RoleEnum.user
    )
    
    # Create and associate user details
    # Remove email from details as it's already in User model
    details_data = user.details.dict()
    if 'email' in details_data:
        details_data.pop('email')
    
    db_user_details = models.UserDetails(**details_data)
    db_user.details = db_user_details
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def get_agent_by_role(db: Session, agent_role: str, language: str = "en"):
    return db.query(models.Agent).filter(models.Agent.agent_role == agent_role, models.Agent.language == language).first()

def get_recent_conversations(db: Session, user_id: int, limit: int = 5):
    return db.query(models.Conversation)\
        .filter(models.Conversation.user_id == user_id)\
        .order_by(models.Conversation.ended_at.desc())\
        .limit(limit)\
        .all()

def get_user_conversations(
    db: Session,
    user_id: int,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    limit: Optional[int] = 30,
    agent_role: Optional[str] = None
) -> List[models.Conversation]:
    query = db.query(models.Conversation).filter(
        models.Conversation.user_id == user_id
    )
    
    if start_date:
        query = query.filter(models.Conversation.created_at >= start_date)
    if end_date:
        query = query.filter(models.Conversation.created_at <= end_date)
    if agent_role:
        query = query.filter(models.Conversation.agent_role == agent_role)
        
    return query.order_by(models.Conversation.created_at.desc()).limit(limit).all()

def create_conversation(
    db: Session,
    user_id: int,
    agent_name: str,
    agent_role: str,
    messages: list,
    metadata: dict = None
) -> models.Conversation:
    """
    Create a new conversation with structured data
    """
    now = datetime.utcnow()
    
    conversation_data = {
        "agent": {
            "name": agent_name,
            "role": agent_role
        },
        "messages": messages,
        "metadata": metadata or {}
    }
    
    db_conversation = models.Conversation(
        user_id=user_id,
        agent_role=agent_role,
        conversation_data=conversation_data,
        started_at=now,
        ended_at=now
    )
    
    db.add(db_conversation)
    db.commit()
    db.refresh(db_conversation)
    return db_conversation

def append_message_to_conversation(db: Session, conversation_id: int, message: Dict[str, Any]):
    conversation = db.query(models.Conversation).filter(models.Conversation.id == conversation_id).first()
    if conversation:
        if conversation.messages is None:
            conversation.messages = []
        conversation.messages = conversation.messages + [message]
        db.commit()
        db.refresh(conversation)
    return conversation

def create_admin_user(db: Session, user: schemas.UserCreate):
    hashed_pw = auth.get_password_hash(user.password)
    db_user = models.User(
        email=user.email,
        hashed_password=hashed_pw,
        role=models.RoleEnum.admin,
        is_approved=True  # Admins are auto-approved
    )
    
    # Create and associate user details
    details_data = user.details.dict()
    if 'email' in details_data:
        details_data.pop('email')
    
    db_user_details = models.UserDetails(**details_data)
    db_user.details = db_user_details
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def get_configuration_by_key(db: Session, key: str):
    """Get configuration value by key from the database"""
    return db.query(models.Configuration).filter(models.Configuration.key == key).first()

def create_or_update_configuration(db: Session, key: str, value: Any):
    """Create a new configuration or update if it exists"""
    config = get_configuration_by_key(db, key)
    
    if config:
        # Update existing configuration
        config.value = value
        config.updated_at = datetime.utcnow()
    else:
        # Create new configuration
        config = models.Configuration(
            key=key,
            value=value,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        db.add(config)
        
    db.commit()
    db.refresh(config)
    return config

def store_conversation(
    db: Session,
    conversation: schemas.ConversationCreate,
    user_id: int
) -> models.Conversation:
    """
    Store a conversation in the database
    """
    from app.logger import app_logger as logger
    import json
    
    now = datetime.utcnow()
    
    # Try to find existing conversation - look for the most recent one with this user and agent
    existing_conversation = db.query(models.Conversation).filter(
        models.Conversation.user_id == user_id,
        models.Conversation.agent_role == conversation.agent_role
    ).order_by(desc(models.Conversation.updated_at)).first()
    
    if existing_conversation:
        logger.info(f"Found existing conversation (ID: {existing_conversation.id}) for user {user_id} with agent {conversation.agent_role}")
        
        # Get existing conversation data
        existing_data = existing_conversation.conversation_data
        new_data = conversation.conversation_data
        
        # Check if both have messages key
        if "messages" in existing_data and "messages" in new_data:
            existing_messages = existing_data["messages"]
            new_messages = new_data["messages"]
            
            logger.info(f"Existing conversation has {len(existing_messages)} messages")
            logger.info(f"New data has {len(new_messages)} messages")
            
            # Check for duplicate messages to avoid adding the same message twice
            # Use a simple approach: create a set of message content for existing messages
            existing_message_contents = {
                f"{msg.get('role')}:{msg.get('content')[:50]}" 
                for msg in existing_messages if 'content' in msg and 'role' in msg
            }
            
            # Only add messages that aren't already in the conversation
            unique_new_messages = []
            for msg in new_messages:
                if 'content' in msg and 'role' in msg:
                    msg_key = f"{msg.get('role')}:{msg.get('content')[:50]}"
                    if msg_key not in existing_message_contents:
                        unique_new_messages.append(msg)
                        existing_message_contents.add(msg_key)
                else:
                    # If message doesn't have content or role, add it anyway
                    unique_new_messages.append(msg)
            
            logger.info(f"Adding {len(unique_new_messages)} unique new messages")
            
            # Create a new combined list of messages
            combined_messages = existing_messages + unique_new_messages
            
            # Update the existing conversation data with the combined messages
            existing_data["messages"] = combined_messages
            
            logger.info(f"Combined conversation now has {len(combined_messages)} messages")
            
            # Update the conversation in the database
            existing_conversation.conversation_data = existing_data
            existing_conversation.updated_at = now
            db.commit()
            db.refresh(existing_conversation)
            
            # Verify the actual number of messages after refresh
            actual_message_count = len(existing_conversation.conversation_data.get("messages", []))
            logger.info(f"Updated conversation in database with {actual_message_count} messages")
            
            return existing_conversation
        else:
            logger.warning("Could not find 'messages' key in one or both conversation data objects")
            logger.warning(f"Existing data keys: {list(existing_data.keys())}")
            logger.warning(f"New data keys: {list(new_data.keys())}")
            
            # Just replace the entire conversation data
            existing_conversation.conversation_data = new_data
            existing_conversation.updated_at = now
            db.commit()
            db.refresh(existing_conversation)
            return existing_conversation
    else:
        logger.info(f"Creating new conversation for user {user_id} with agent {conversation.agent_role}")
        
        # Create new conversation
        new_conversation = models.Conversation(
            user_id=user_id,
            agent_role=conversation.agent_role,
            conversation_data=conversation.conversation_data,
            created_at=now,
            updated_at=now
        )
        db.add(new_conversation)
        db.commit()
        db.refresh(new_conversation)
        
        logger.info(f"Created new conversation (ID: {new_conversation.id}) with {len(new_conversation.conversation_data.get('messages', []))} messages")

        return new_conversation


# Common Feedback CRUD operations
def create_common_feedback(
    db: Session,
    user_id: int,
    conversation_data: dict
) -> models.CommonFeedback:
    """
    Create a new common feedback entry
    """
    db_feedback = models.CommonFeedback(
        user_id=user_id,
        conversation_data=conversation_data
    )

    db.add(db_feedback)
    db.commit()
    db.refresh(db_feedback)
    return db_feedback


def get_common_feedbacks_by_user(
    db: Session,
    user_id: int,
    limit: int = 30
) -> List[models.CommonFeedback]:
    """
    Get common feedback entries for a specific user
    """
    return db.query(models.CommonFeedback).filter(
        models.CommonFeedback.user_id == user_id
    ).order_by(models.CommonFeedback.created_at.desc()).limit(limit).all()


def get_all_common_feedbacks(
    db: Session,
    limit: int = 100,
    user_id: int = None,
    start_date: datetime = None,
    end_date: datetime = None
) -> List[models.CommonFeedback]:
    """
    Get all common feedback entries with optional filters
    """
    query = db.query(models.CommonFeedback)

    if user_id:
        query = query.filter(models.CommonFeedback.user_id == user_id)

    if start_date:
        query = query.filter(models.CommonFeedback.created_at >= start_date)

    if end_date:
        query = query.filter(models.CommonFeedback.created_at <= end_date)

    return query.order_by(models.CommonFeedback.created_at.desc()).limit(limit).all()


# Frontend Log CRUD operations
def create_frontend_log(
    db: Session,
    user_id: int,
    log_data: dict,
    log_type: Optional[str] = None
) -> models.FrontendLog:
    """
    Create a new frontend log entry
    """
    db_log = models.FrontendLog(
        user_id=user_id,
        log_data=log_data,
        log_type=log_type
    )

    db.add(db_log)
    db.commit()
    db.refresh(db_log)
    return db_log


def create_frontend_logs_batch(
    db: Session,
    user_id: int,
    logs: List[dict]
) -> List[models.FrontendLog]:
    """
    Create multiple frontend log entries in batch
    """
    db_logs = []
    for log_item in logs:
        db_log = models.FrontendLog(
            user_id=user_id,
            log_data=log_item.get('log_data', {}),
            log_type=log_item.get('log_type')
        )
        db_logs.append(db_log)

    db.add_all(db_logs)
    db.commit()

    # Refresh all objects
    for db_log in db_logs:
        db.refresh(db_log)

    return db_logs


def get_frontend_logs_by_user(
    db: Session,
    user_id: int,
    limit: int = 30,
    log_type: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None
) -> List[models.FrontendLog]:
    """
    Get frontend log entries for a specific user
    """
    query = db.query(models.FrontendLog).filter(
        models.FrontendLog.user_id == user_id
    )

    if log_type:
        query = query.filter(models.FrontendLog.log_type == log_type)

    if start_date:
        query = query.filter(models.FrontendLog.created_at >= start_date)

    if end_date:
        query = query.filter(models.FrontendLog.created_at <= end_date)

    return query.order_by(models.FrontendLog.created_at.desc()).limit(limit).all()


def get_all_frontend_logs(
    db: Session,
    limit: int = 30,
    user_id: Optional[int] = None,
    log_type: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None
) -> List[models.FrontendLog]:
    """
    Get all frontend log entries with optional filters (admin only)
    """
    query = db.query(models.FrontendLog)

    if user_id:
        query = query.filter(models.FrontendLog.user_id == user_id)

    if log_type:
        query = query.filter(models.FrontendLog.log_type == log_type)

    if start_date:
        query = query.filter(models.FrontendLog.created_at >= start_date)

    if end_date:
        query = query.filter(models.FrontendLog.created_at <= end_date)

    return query.order_by(models.FrontendLog.created_at.desc()).limit(limit).all()


def get_frontend_log_by_id(
    db: Session,
    log_id: int
) -> Optional[models.FrontendLog]:
    """
    Get a specific frontend log entry by ID
    """
    return db.query(models.FrontendLog).filter(models.FrontendLog.id == log_id).first()


def get_common_feedback_by_id(
    db: Session,
    feedback_id: int
) -> models.CommonFeedback:
    """
    Get a specific common feedback entry by ID
    """
    return db.query(models.CommonFeedback).filter(
        models.CommonFeedback.id == feedback_id
    ).first()
