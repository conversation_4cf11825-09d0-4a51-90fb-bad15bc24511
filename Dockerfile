FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install alembic for migrations
RUN pip install alembic

# Copy the rest of the application
COPY . .

# Add this after copying files
COPY run_migrations.sh /app/
RUN chmod +x /app/run_migrations.sh

# Make create_tables.py executable
RUN chmod +x /app/create_tables.py

# Expose the port the app runs on
EXPOSE 8000

# Command to run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
