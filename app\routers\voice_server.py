from fastapi import APIRouter, Depends, WebSocket
from sqlalchemy.orm import Session
from app.database import get_db
from app.websocket.voice_handler import VoiceWebSocketHandler
from app.logger import app_logger as logger

router = APIRouter(tags=["voice"])


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    agent_role: str = None,
    language: str = "en",
    db: Session = Depends(get_db)
):
    """
    WebSocket endpoint for voice interactions

    Args:
        websocket: WebSocket connection
        agent_role: Role of the agent (oba, fa, ppca, etc.)
        language: Language for the conversation (en, de)
        db: Database session
    """
    handler = VoiceWebSocketHandler()
    await handler.handle_connection(websocket, agent_role, language, db)
